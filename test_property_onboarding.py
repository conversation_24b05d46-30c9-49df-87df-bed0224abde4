#!/usr/bin/env python3
"""
Test script for property onboarding flow
"""

import requests
import json

def test_property_onboarding():
    """Test the complete property onboarding flow"""
    
    # Test data
    property_id = "TEST_PROP_001"
    brand_id = 1
    
    # API endpoint
    url = f"http://localhost:5000/api/properties/{property_id}/onboard"
    
    # Request payload
    payload = {
        "brand_id": brand_id,
        "auto_create_departments": True,
        "auto_create_profit_centers": True,
        "auto_activate_skus": True,
        "enable_tax_calculation": True,
        "custom_config": {}
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"Testing property onboarding for property: {property_id}")
    print(f"Request URL: {url}")
    print(f"Request payload: {json.dumps(payload, indent=2)}")
    
    try:
        # Make the API call
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"\nResponse Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            response_data = response.json()
            print(f"\n✅ Property onboarding successful!")
            print(f"Response: {json.dumps(response_data, indent=2)}")
            
            # Print summary
            print(f"\n📊 Onboarding Summary:")
            print(f"   - Property ID: {response_data.get('property_id')}")
            print(f"   - Brand ID: {response_data.get('brand_id')}")
            print(f"   - Departments Created: {response_data.get('departments_created', 0)}")
            print(f"   - Profit Centers Created: {response_data.get('profit_centers_created', 0)}")
            print(f"   - SKUs Created: {response_data.get('skus_created', 0)}")
            print(f"   - Transaction Codes Generated: {response_data.get('transaction_codes_generated', 0)}")
            print(f"   - Status: {response_data.get('onboarding_status')}")
            
            if response_data.get('errors'):
                print(f"   - Errors: {response_data.get('errors')}")
            
            if response_data.get('warnings'):
                print(f"   - Warnings: {response_data.get('warnings')}")
                
        else:
            print(f"\n❌ Property onboarding failed!")
            try:
                error_data = response.json()
                print(f"Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error response (raw): {response.text}")
                
    except requests.exceptions.ConnectionError:
        print(f"\n❌ Connection error: Could not connect to the API server")
        print(f"   Make sure the cataloging service is running on localhost:5000")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")

def test_onboarding_status():
    """Test getting onboarding status"""
    
    property_id = "TEST_PROP_001"
    url = f"http://localhost:5000/api/properties/{property_id}/onboard/status"
    
    print(f"\n\nTesting onboarding status for property: {property_id}")
    print(f"Request URL: {url}")
    
    try:
        response = requests.get(url)
        
        print(f"\nResponse Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"\n✅ Onboarding status retrieved successfully!")
            print(f"Status: {json.dumps(response_data, indent=2)}")
        else:
            print(f"\n❌ Failed to get onboarding status!")
            try:
                error_data = response.json()
                print(f"Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error response (raw): {response.text}")
                
    except requests.exceptions.ConnectionError:
        print(f"\n❌ Connection error: Could not connect to the API server")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Property Onboarding Tests")
    print("=" * 50)
    
    # Test property onboarding
    test_property_onboarding()
    
    # Test onboarding status
    test_onboarding_status()
    
    print("\n" + "=" * 50)
    print("✅ Tests completed!")
