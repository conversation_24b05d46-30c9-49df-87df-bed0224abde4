#!/usr/bin/env python3
"""
Comprehensive test script for property onboarding flow
Updated with latest API changes, schema responses, and enhanced testing
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional

class PropertyOnboardingTester:
    """Comprehensive property onboarding test suite"""
    
    def __init__(self, base_url: str = "http://localhost:5000", property_id: str = "0001263", brand_id: int = 1):
        self.base_url = base_url
        self.property_id = property_id
        self.brand_id = brand_id
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
        
    def test_property_onboarding_full_flow(self):
        """Test the complete property onboarding flow with latest schema"""
        
        print(f"🚀 Testing Property Onboarding Flow")
        print(f"Property ID: {self.property_id}")
        print(f"Brand ID: {self.brand_id}")
        print("=" * 60)
        
        # Test 1: Basic onboarding with all features enabled
        self._test_basic_onboarding()
        
        # Test 2: Selective feature onboarding
        self._test_selective_onboarding()
        
        # Test 3: Custom configuration onboarding
        self._test_custom_config_onboarding()
        
        # Test 4: Idempotency test (run same onboarding twice)
        self._test_idempotency()
        
        # Test 5: Error handling tests
        self._test_error_scenarios()
        
    def _test_basic_onboarding(self):
        """Test basic onboarding with all features enabled"""
        print("\n📋 Test 1: Basic Onboarding (All Features)")
        print("-" * 40)
        
        # Updated API endpoint based on current implementation
        url = f"{self.base_url}/api/property/onboard"
        
        # Updated payload based on latest schema
        payload = {
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }
        
        print(f"Request URL: {url}")
        print(f"Request payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = self.session.post(url, json=payload)
            self._process_onboarding_response(response, "Basic Onboarding")
            
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection error: Could not connect to {self.base_url}")
            print("   Make sure the cataloging service is running")
        except Exception as e:
            print(f"❌ Unexpected error: {str(e)}")
    
    def _test_selective_onboarding(self):
        """Test onboarding with selective features"""
        print("\n📋 Test 2: Selective Feature Onboarding")
        print("-" * 40)
        
        url = f"{self.base_url}/api/property/onboard"
        
        # Test with only departments enabled
        payload = {
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": False,
            "custom_config": {
                "department_filter": "FINANCE,HOUSEKEEPING"
            }
        }
        
        print(f"Request payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = self.session.post(url, json=payload)
            self._process_onboarding_response(response, "Selective Onboarding")
            
        except Exception as e:
            print(f"❌ Error in selective onboarding: {str(e)}")
    
    def _test_custom_config_onboarding(self):
        """Test onboarding with custom configuration"""
        print("\n📋 Test 3: Custom Configuration Onboarding")
        print("-" * 40)
        
        url = f"{self.base_url}/api/property/onboard"
        
        payload = {
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {
                "template_filters": {
                    "department_codes": ["FINANCE", "HOUSEKEEPING"],
                    "profit_center_codes": ["FRONTDESK", "BAR"]
                },
                "override_settings": {
                    "force_recreate": False,
                    "skip_existing": True
                }
            }
        }
        
        print(f"Request payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = self.session.post(url, json=payload)
            self._process_onboarding_response(response, "Custom Config Onboarding")
            
        except Exception as e:
            print(f"❌ Error in custom config onboarding: {str(e)}")
    
    def _test_idempotency(self):
        """Test that onboarding can be run multiple times safely"""
        print("\n📋 Test 4: Idempotency Test")
        print("-" * 40)
        
        url = f"{self.base_url}/api/property/onboard"
        
        payload = {
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }
        
        print("Running onboarding twice to test idempotency...")
        
        try:
            # First run
            print("\n🔄 First run:")
            response1 = self.session.post(url, json=payload)
            result1 = self._process_onboarding_response(response1, "Idempotency Test - Run 1", return_data=True)
            
            time.sleep(1)  # Small delay
            
            # Second run
            print("\n🔄 Second run:")
            response2 = self.session.post(url, json=payload)
            result2 = self._process_onboarding_response(response2, "Idempotency Test - Run 2", return_data=True)
            
            # Compare results
            if result1 and result2:
                self._compare_idempotency_results(result1, result2)
            
        except Exception as e:
            print(f"❌ Error in idempotency test: {str(e)}")
    
    def _test_error_scenarios(self):
        """Test various error scenarios"""
        print("\n📋 Test 5: Error Scenario Testing")
        print("-" * 40)
        
        # Test 5a: Invalid property ID
        self._test_invalid_property_id()
        
        # Test 5b: Invalid brand ID
        self._test_invalid_brand_id()
        
        # Test 5c: Missing required fields
        self._test_missing_fields()
    
    def _test_invalid_property_id(self):
        """Test with invalid property ID"""
        print("\n🔍 Test 5a: Invalid Property ID")
        
        url = f"{self.base_url}/api/property/onboard"
        payload = {
            "property_id": "INVALID_PROP_999",
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }
        
        try:
            response = self.session.post(url, json=payload)
            self._process_error_response(response, "Invalid Property ID")
        except Exception as e:
            print(f"❌ Error testing invalid property ID: {str(e)}")
    
    def _test_invalid_brand_id(self):
        """Test with invalid brand ID"""
        print("\n🔍 Test 5b: Invalid Brand ID")
        
        url = f"{self.base_url}/api/property/onboard"
        payload = {
            "property_id": self.property_id,
            "brand_id": 99999,  # Invalid brand ID
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }
        
        try:
            response = self.session.post(url, json=payload)
            self._process_error_response(response, "Invalid Brand ID")
        except Exception as e:
            print(f"❌ Error testing invalid brand ID: {str(e)}")
    
    def _test_missing_fields(self):
        """Test with missing required fields"""
        print("\n🔍 Test 5c: Missing Required Fields")
        
        url = f"{self.base_url}/api/property/onboard"
        payload = {
            "property_id": self.property_id,
            # Missing brand_id
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }
        
        try:
            response = self.session.post(url, json=payload)
            self._process_error_response(response, "Missing Brand ID")
        except Exception as e:
            print(f"❌ Error testing missing fields: {str(e)}")
    
    def _process_onboarding_response(self, response: requests.Response, test_name: str, return_data: bool = False) -> Optional[Dict[str, Any]]:
        """Process and display onboarding response"""
        print(f"\nResponse Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
        except:
            response_data = None
            print(f"Response (raw): {response.text}")
        
        if response.status_code == 201:
            print(f"✅ {test_name} successful!")
            if response_data:
                print(f"Response: {json.dumps(response_data, indent=2, default=str)}")
                self._display_onboarding_summary(response_data)
                if return_data:
                    return response_data
        else:
            print(f"❌ {test_name} failed!")
            if response_data:
                print(f"Error response: {json.dumps(response_data, indent=2, default=str)}")
            
        return None
    
    def _process_error_response(self, response: requests.Response, test_name: str):
        """Process and display error response"""
        print(f"Response Status Code: {response.status_code}")
        
        if response.status_code in [400, 404, 422]:
            print(f"✅ {test_name} correctly returned error status")
        else:
            print(f"⚠️ {test_name} returned unexpected status: {response.status_code}")
        
        try:
            error_data = response.json()
            print(f"Error response: {json.dumps(error_data, indent=2, default=str)}")
        except:
            print(f"Error response (raw): {response.text}")
    
    def _display_onboarding_summary(self, response_data: Dict[str, Any]):
        """Display formatted onboarding summary"""
        print(f"\n📊 Onboarding Summary:")
        print(f"   - Property ID: {response_data.get('property_id', 'N/A')}")
        print(f"   - Brand ID: {response_data.get('brand_id', 'N/A')}")
        print(f"   - Departments Created: {response_data.get('departments_created', 0)}")
        print(f"   - Profit Centers Created: {response_data.get('profit_centers_created', 0)}")
        print(f"   - Status: {response_data.get('onboarding_status', 'N/A')}")
        print(f"   - Onboarded At: {response_data.get('onboarded_at', 'N/A')}")
        
        if response_data.get('errors'):
            print(f"   - Errors ({len(response_data['errors'])}):")
            for i, error in enumerate(response_data['errors'], 1):
                print(f"     {i}. {error}")
        
        if response_data.get('warnings'):
            print(f"   - Warnings ({len(response_data['warnings'])}):")
            for i, warning in enumerate(response_data['warnings'], 1):
                print(f"     {i}. {warning}")
    
    def _compare_idempotency_results(self, result1: Dict[str, Any], result2: Dict[str, Any]):
        """Compare results from idempotency test"""
        print(f"\n🔍 Idempotency Analysis:")
        
        # Compare key metrics
        fields_to_compare = ['departments_created', 'profit_centers_created', 'onboarding_status']
        
        for field in fields_to_compare:
            val1 = result1.get(field, 'N/A')
            val2 = result2.get(field, 'N/A')
            
            if val1 == val2:
                print(f"   ✅ {field}: {val1} (consistent)")
            else:
                print(f"   ⚠️ {field}: {val1} → {val2} (changed)")
        
        # Check if second run had fewer creations (indicating skipping of existing items)
        if (result2.get('departments_created', 0) <= result1.get('departments_created', 0) and
            result2.get('profit_centers_created', 0) <= result1.get('profit_centers_created', 0)):
            print(f"   ✅ Idempotency: Second run correctly skipped existing items")
        else:
            print(f"   ⚠️ Idempotency: Second run created more items than expected")


def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🚀 Starting Comprehensive Property Onboarding Tests")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("=" * 60)
    
    # Initialize tester
    tester = PropertyOnboardingTester()
    
    # Run full test suite
    tester.test_property_onboarding_full_flow()
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print(f"Completed at: {datetime.now().isoformat()}")


    def test_schema_validation(self):
        """Test request and response schema validation"""
        print("\n📋 Test 6: Schema Validation")
        print("-" * 40)

        # Test valid schema
        self._test_valid_schema()

        # Test invalid schema types
        self._test_invalid_schema_types()

        # Test boundary values
        self._test_boundary_values()

    def _test_valid_schema(self):
        """Test with valid schema"""
        print("\n🔍 Test 6a: Valid Schema")

        url = f"{self.base_url}/api/property/onboard"
        payload = {
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {
                "nested_object": {
                    "key": "value"
                },
                "array_field": ["item1", "item2"]
            }
        }

        try:
            response = self.session.post(url, json=payload)
            if response.status_code == 201:
                print("✅ Valid schema accepted")
            else:
                print(f"⚠️ Valid schema rejected with status: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing valid schema: {str(e)}")

    def _test_invalid_schema_types(self):
        """Test with invalid schema types"""
        print("\n🔍 Test 6b: Invalid Schema Types")

        url = f"{self.base_url}/api/property/onboard"

        # Test with string brand_id instead of int
        payload = {
            "property_id": self.property_id,
            "brand_id": "invalid_string",  # Should be int
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }

        try:
            response = self.session.post(url, json=payload)
            if response.status_code == 422:
                print("✅ Invalid brand_id type correctly rejected")
            else:
                print(f"⚠️ Invalid brand_id type not properly validated: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing invalid schema types: {str(e)}")

    def _test_boundary_values(self):
        """Test with boundary values"""
        print("\n🔍 Test 6c: Boundary Values")

        url = f"{self.base_url}/api/property/onboard"

        # Test with very large brand_id
        payload = {
            "property_id": self.property_id,
            "brand_id": 2147483647,  # Max int32
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }

        try:
            response = self.session.post(url, json=payload)
            print(f"Large brand_id test result: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing boundary values: {str(e)}")

    def test_api_endpoints(self):
        """Test various API endpoint scenarios"""
        print("\n📋 Test 7: API Endpoint Testing")
        print("-" * 40)

        # Test different HTTP methods
        self._test_http_methods()

        # Test content types
        self._test_content_types()

        # Test authentication (if applicable)
        self._test_authentication()

    def _test_http_methods(self):
        """Test different HTTP methods"""
        print("\n🔍 Test 7a: HTTP Methods")

        url = f"{self.base_url}/api/property/onboard"

        # Test GET (should not be allowed)
        try:
            response = self.session.get(url)
            if response.status_code == 405:
                print("✅ GET method correctly rejected (405)")
            else:
                print(f"⚠️ GET method response: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing GET method: {str(e)}")

        # Test PUT (should not be allowed)
        try:
            response = self.session.put(url, json={})
            if response.status_code == 405:
                print("✅ PUT method correctly rejected (405)")
            else:
                print(f"⚠️ PUT method response: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing PUT method: {str(e)}")

    def _test_content_types(self):
        """Test different content types"""
        print("\n🔍 Test 7b: Content Types")

        url = f"{self.base_url}/api/property/onboard"
        payload = {
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {}
        }

        # Test with wrong content type
        try:
            response = self.session.post(
                url,
                data=json.dumps(payload),
                headers={"Content-Type": "text/plain"}
            )
            if response.status_code == 400:
                print("✅ Wrong content type correctly rejected")
            else:
                print(f"⚠️ Wrong content type response: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing content types: {str(e)}")

    def _test_authentication(self):
        """Test authentication scenarios"""
        print("\n🔍 Test 7c: Authentication")

        # This is a placeholder - implement based on your auth requirements
        print("ℹ️ Authentication testing not implemented (depends on auth setup)")

    def generate_test_report(self):
        """Generate a comprehensive test report"""
        print("\n📊 Generating Test Report")
        print("=" * 60)

        report = {
            "test_run_timestamp": datetime.now().isoformat(),
            "property_id": self.property_id,
            "brand_id": self.brand_id,
            "base_url": self.base_url,
            "tests_executed": [
                "Basic Onboarding",
                "Selective Onboarding",
                "Custom Configuration",
                "Idempotency",
                "Error Scenarios",
                "Schema Validation",
                "API Endpoints"
            ],
            "recommendations": [
                "Verify all department templates are properly configured",
                "Ensure profit center templates have correct department mappings",
                "Test with different brand configurations",
                "Monitor performance with large datasets",
                "Implement proper error logging and monitoring"
            ]
        }

        print(f"Test Report: {json.dumps(report, indent=2, default=str)}")

        # Save report to file
        report_filename = f"property_onboarding_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"\n📄 Test report saved to: {report_filename}")
        except Exception as e:
            print(f"❌ Failed to save test report: {str(e)}")


def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🚀 Starting Comprehensive Property Onboarding Tests")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("=" * 60)

    # Initialize tester
    tester = PropertyOnboardingTester()

    # Run full test suite
    tester.test_property_onboarding_full_flow()

    # Run schema validation tests
    tester.test_schema_validation()

    # Run API endpoint tests
    tester.test_api_endpoints()

    # Generate test report
    tester.generate_test_report()

    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print(f"Completed at: {datetime.now().isoformat()}")


def run_quick_test():
    """Run a quick basic test"""
    print("⚡ Running Quick Property Onboarding Test")
    print("=" * 50)

    tester = PropertyOnboardingTester()
    tester._test_basic_onboarding()

    print("\n✅ Quick test completed!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        run_quick_test()
    else:
        run_comprehensive_tests()
