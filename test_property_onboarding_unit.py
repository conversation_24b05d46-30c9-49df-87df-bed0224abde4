#!/usr/bin/env python3
"""
Unit tests for property onboarding flow
Compatible with pytest framework
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Import the schemas and services to test
from cataloging_service.schemas.property_onboarding import (
    PropertyOnboardingRequestSchema,
    PropertyOnboardingResponseSchema
)
from cataloging_service.domain.services.template.property_onboarding_service import PropertyOnboardingService
from cataloging_service.domain.entities.properties.property_onboarding import PropertyOnboardingEntity


class TestPropertyOnboardingSchemas:
    """Test property onboarding request and response schemas"""
    
    def test_request_schema_valid(self):
        """Test valid request schema"""
        valid_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {"key": "value"}
        }
        
        schema = PropertyOnboardingRequestSchema.model_validate(valid_data)
        assert schema.property_id == "PROP123"
        assert schema.brand_id == 1
        assert schema.auto_create_departments is True
        assert schema.auto_create_profit_centers is True
        assert schema.custom_config == {"key": "value"}
    
    def test_request_schema_defaults(self):
        """Test request schema with default values"""
        minimal_data = {
            "property_id": "PROP123",
            "brand_id": 1
        }
        
        schema = PropertyOnboardingRequestSchema.model_validate(minimal_data)
        assert schema.auto_create_departments is True  # Default value
        assert schema.auto_create_profit_centers is True  # Default value
        assert schema.custom_config == {}  # Default empty dict
    
    def test_request_schema_invalid_brand_id(self):
        """Test request schema with invalid brand_id"""
        invalid_data = {
            "property_id": "PROP123",
            "brand_id": -1,  # Negative brand_id should be invalid
            "auto_create_departments": True,
            "auto_create_profit_centers": True
        }
        
        with pytest.raises(ValueError):
            PropertyOnboardingRequestSchema.model_validate(invalid_data)
    
    def test_request_schema_missing_required_fields(self):
        """Test request schema with missing required fields"""
        invalid_data = {
            "property_id": "PROP123"
            # Missing brand_id
        }
        
        with pytest.raises(ValueError):
            PropertyOnboardingRequestSchema.model_validate(invalid_data)
    
    def test_response_schema_valid(self):
        """Test valid response schema"""
        valid_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "departments_created": 3,
            "profit_centers_created": 2,
            "onboarding_status": "COMPLETED",
            "errors": [],
            "warnings": ["Some warning"],
            "onboarded_at": datetime.now()
        }
        
        schema = PropertyOnboardingResponseSchema.model_validate(valid_data)
        assert schema.property_id == "PROP123"
        assert schema.brand_id == 1
        assert schema.departments_created == 3
        assert schema.profit_centers_created == 2
        assert schema.onboarding_status == "COMPLETED"
        assert len(schema.warnings) == 1


class TestPropertyOnboardingEntity:
    """Test property onboarding entity business logic"""
    
    def test_entity_creation(self):
        """Test entity creation with valid data"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1,
            template_filters={"key": "value"}
        )
        
        assert entity.property_id == "PROP123"
        assert entity.brand_id == 1
        assert entity.template_filters == {"key": "value"}
        assert entity.onboarding_status == "PENDING"
        assert entity.departments_created == 0
        assert entity.profit_centers_created == 0
    
    def test_entity_completion(self):
        """Test entity completion logic"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1
        )
        
        # Initially not completed
        assert not entity.is_completed()
        
        # Mark as completed
        entity.mark_completed()
        assert entity.onboarding_status == "COMPLETED"
        assert entity.is_completed()
        assert entity.onboarded_at is not None
    
    def test_entity_error_handling(self):
        """Test entity error handling"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1
        )
        
        # Add error
        entity.add_error("Test error")
        assert len(entity.errors) == 1
        assert "Test error" in entity.errors
        
        # Entity with errors should not be completed
        entity.mark_completed()
        assert not entity.is_completed()  # Has errors
    
    def test_entity_warnings(self):
        """Test entity warning handling"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1
        )
        
        # Add warning
        entity.add_warning("Test warning")
        assert len(entity.warnings) == 1
        assert entity.has_warnings()
        
        # Entity with warnings can still be completed
        entity.mark_completed()
        assert entity.is_completed()  # Warnings don't prevent completion


class TestPropertyOnboardingService:
    """Test property onboarding service"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies for the service"""
        return {
            'department_template_service': Mock(),
            'profit_center_template_service': Mock(),
            'property_department_service': Mock(),
            'transaction_master_service': Mock()
        }
    
    @pytest.fixture
    def onboarding_service(self, mock_dependencies):
        """Create onboarding service with mocked dependencies"""
        service = PropertyOnboardingService(**mock_dependencies)
        
        # Mock the service provider dependencies
        service.property_service = Mock()
        service.meta_service = Mock()
        service.seller_service = Mock()
        service.sku_service = Mock()
        
        return service
    
    def test_validate_property_success(self, onboarding_service):
        """Test successful property validation"""
        # Mock property service to return a valid property
        mock_property = Mock()
        mock_property.id = "PROP123"
        onboarding_service.property_service.get_property.return_value = mock_property
        
        result = onboarding_service._validate_property("PROP123")
        assert result == mock_property
        onboarding_service.property_service.get_property.assert_called_once_with("PROP123")
    
    def test_validate_property_not_found(self, onboarding_service):
        """Test property validation when property not found"""
        # Mock property service to return None
        onboarding_service.property_service.get_property.return_value = None
        
        with pytest.raises(ValueError, match="Property with ID 'INVALID' not found"):
            onboarding_service._validate_property("INVALID")
    
    def test_generate_profit_center_id(self, onboarding_service):
        """Test profit center ID generation"""
        with patch('cataloging_service.domain.services.template.property_onboarding_service.datetime') as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2023-01-01T00:00:00"
            
            profit_center_id = onboarding_service._generate_profit_center_id("PROP123", "FRONTDESK")
            
            assert profit_center_id.startswith("PC_PROP123_")
            assert len(profit_center_id) > 10  # Should have hash suffix
    
    def test_get_city_from_property_direct_city(self, onboarding_service):
        """Test getting city from property with direct city relationship"""
        mock_property = Mock()
        mock_city = Mock()
        mock_city.id = 1
        mock_property.city = mock_city
        
        result = onboarding_service._get_city_from_property(mock_property)
        assert result == mock_city
    
    def test_get_city_from_property_via_location(self, onboarding_service):
        """Test getting city from property via location relationship"""
        mock_property = Mock()
        mock_property.city = None  # No direct city
        
        mock_location = Mock()
        mock_city = Mock()
        mock_city.id = 1
        mock_location.city = mock_city
        mock_property.location = mock_location
        
        result = onboarding_service._get_city_from_property(mock_property)
        assert result == mock_city
    
    def test_get_city_from_property_via_city_id(self, onboarding_service):
        """Test getting city from property via city_id"""
        mock_property = Mock()
        mock_property.city = None  # No direct city
        mock_property.location = None  # No location
        mock_property.city_id = 1
        
        mock_city = Mock()
        mock_city.id = 1
        onboarding_service.meta_service.get_city_by_id.return_value = mock_city
        
        result = onboarding_service._get_city_from_property(mock_property)
        assert result == mock_city
        onboarding_service.meta_service.get_city_by_id.assert_called_once_with(1)
    
    def test_get_city_from_property_not_found(self, onboarding_service):
        """Test getting city from property when city not found"""
        mock_property = Mock()
        mock_property.city = None
        mock_property.location = None
        mock_property.city_id = None
        
        result = onboarding_service._get_city_from_property(mock_property)
        assert result is None
    
    @patch('cataloging_service.domain.services.template.property_onboarding_service.logger')
    def test_onboard_property_success(self, mock_logger, onboarding_service):
        """Test successful property onboarding"""
        # Setup mocks
        onboarding_service.property_service.get_property.return_value = Mock()
        onboarding_service.department_template_service.get_auto_create_templates.return_value = []
        onboarding_service.profit_center_template_service.get_auto_create_templates.return_value = []
        onboarding_service.sku_service.get_skus_with_department_templates.return_value = []
        
        # Create request
        request = PropertyOnboardingRequestSchema.model_validate({
            "property_id": "PROP123",
            "brand_id": 1,
            "auto_create_departments": True,
            "auto_create_profit_centers": True
        })
        
        # Execute onboarding
        result = onboarding_service.onboard_property(request)
        
        # Verify result
        assert isinstance(result, PropertyOnboardingResponseSchema)
        assert result.property_id == "PROP123"
        assert result.brand_id == 1
        assert result.onboarding_status == "COMPLETED"
    
    def test_onboard_property_with_error(self, onboarding_service):
        """Test property onboarding with error"""
        # Setup mock to raise exception
        onboarding_service.property_service.get_property.side_effect = Exception("Test error")
        
        # Create request
        request = PropertyOnboardingRequestSchema.model_validate({
            "property_id": "PROP123",
            "brand_id": 1
        })
        
        # Execute onboarding
        result = onboarding_service.onboard_property(request)
        
        # Verify error handling
        assert isinstance(result, PropertyOnboardingResponseSchema)
        assert len(result.errors) > 0
        assert "Test error" in result.errors[0]


class TestPropertyOnboardingIntegration:
    """Integration tests for property onboarding"""
    
    def test_request_response_schema_compatibility(self):
        """Test that request and response schemas are compatible"""
        request_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {"test": "value"}
        }
        
        response_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "departments_created": 2,
            "profit_centers_created": 1,
            "onboarding_status": "COMPLETED",
            "errors": [],
            "warnings": [],
            "onboarded_at": datetime.now()
        }
        
        # Both should validate successfully
        request_schema = PropertyOnboardingRequestSchema.model_validate(request_data)
        response_schema = PropertyOnboardingResponseSchema.model_validate(response_data)
        
        # Key fields should match
        assert request_schema.property_id == response_schema.property_id
        assert request_schema.brand_id == response_schema.brand_id


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
