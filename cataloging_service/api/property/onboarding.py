from flask import Blueprint
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.property_onboarding import (
    PropertyOnboardingRequestSchema,
)
from cataloging_service.domain.services.template.property_onboarding_service import PropertyOnboardingService
from cataloging_service.common.api_responses import ApiResponse
from cataloging_service.common.request_decorators import parse_body
from object_registry import inject

bp = Blueprint("property_onboarding", __name__)


@bp.route("/property/onboard", methods=["POST"])
@inject(onboarding_service=PropertyOnboardingService)
@parse_body(PropertyOnboardingRequestSchema)
@atomic_operation
def onboard_property(data: PropertyOnboardingRequestSchema, onboarding_service):
    """
    Complete property onboarding workflow
    ---
    tags:
      - Property Onboarding
    summary: Onboard property with complete workflow
    description: |
      Executes the complete property onboarding workflow including:
      - Auto-create departments from templates where auto_create_on_property_launch=true
      - Auto-create profit centers from templates where auto_create_on_property_launch=true
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertyOnboardingRequestSchema'
          example:
            brand_id: 1
            property_id: "PROP123"
            auto_create_departments: true
            auto_create_profit_centers: true
    responses:
      201:
        description: Property onboarded successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertyOnboardingResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Property not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    # Execute complete onboarding workflow
    onboarding_result = onboarding_service.onboard_property(data)
    return ApiResponse.created(onboarding_result.model_dump())
