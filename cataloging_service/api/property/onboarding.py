from flask import Blueprint
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.property_onboarding import (
    PropertyOnboardingRequestSchema,
    PropertyOnboardingResponseSchema,
)
from cataloging_service.domain.services.template.property_onboarding_service import PropertyOnboardingService
from cataloging_service.common.api_responses import ApiResponse, api_endpoint
from cataloging_service.common.request_decorators import parse_body
from object_registry import inject

bp = Blueprint("property_onboarding", __name__)


@bp.route("/<property_id>/onboard", methods=["POST"])
@inject(onboarding_service=PropertyOnboardingService)
@parse_body(PropertyOnboardingRequestSchema)
@api_endpoint
@atomic_operation
def onboard_property(property_id: str, data: PropertyOnboardingRequestSchema, onboarding_service):
    """
    Complete property onboarding workflow
    ---
    tags:
      - Property Onboarding
    summary: Onboard property with complete workflow
    description: |
      Executes the complete property onboarding workflow including:
      - Auto-create departments from templates where auto_create_on_property_launch=true
      - Auto-create profit centers from templates where auto_create_on_property_launch=true
      - Generate transaction codes based on transaction_default_mapping templates
      - Retrieve tax information with CGST/SGST at 2.5% each
      - Activate SKUs based on template configurations (auto_create_seller_sku=true or default_department_template_code set)
    parameters:
      - name: property_id
        in: path
        required: true
        schema:
          type: string
        description: Property ID to onboard
        example: "PROP123"
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertyOnboardingRequestSchema'
          example:
            brand_id: 1
            auto_create_departments: true
            auto_create_profit_centers: true
            auto_activate_skus: true
            enable_tax_calculation: true
    responses:
      201:
        description: Property onboarded successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertyOnboardingResponseSchema'
      400:
        description: Bad request - validation errors
        content:
          application/json:
            schema:
              $ref: '#/components/responses/BadRequest'
      404:
        description: Property not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    # Execute complete onboarding workflow
    onboarding_result = onboarding_service.onboard_property(onboarding_data=data)
    response_data = PropertyOnboardingResponseSchema.model_validate(onboarding_result).model_dump()
    return ApiResponse.created(response_data)


@bp.route("/<property_id>/onboard/status", methods=["GET"])
@inject(onboarding_service=PropertyOnboardingService)
@api_endpoint
def get_onboarding_status(property_id: str, onboarding_service):
    """
    Get property onboarding status
    ---
    tags:
      - Property Onboarding
    summary: Get onboarding status
    description: |
      Retrieves the current onboarding status for a property including:
      - Department creation status
      - Transaction generation status
      - SKU activation status
      - Service integration status
    parameters:
      - name: property_id
        in: path
        required: true
        schema:
          type: string
        description: Property ID
        example: "PROP123"
    responses:
      200:
        description: Onboarding status retrieved successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertyOnboardingResponseSchema'
      404:
        description: Property not found
        content:
          application/json:
            schema:
              $ref: '#/components/responses/NotFound'
    """
    # Get onboarding status
    status = onboarding_service.get_onboarding_status(property_id)

    if not status:
        return ApiResponse.not_found("Property onboarding status", property_id)

    # Convert to response schema
    response_data = PropertyOnboardingResponseSchema.model_validate(status)
    return ApiResponse.success(response_data.model_dump())