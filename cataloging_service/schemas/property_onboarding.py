"""
Property onboarding schemas for request/response validation
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import Field
from pydantic.types import PositiveInt

from cataloging_service.common.schema_registry import swag_schema
from cataloging_service.schemas.common import BasePydanticAPISchema


@swag_schema
class PropertyOnboardingRequestSchema(BasePydanticAPISchema):
    """Schema for property onboarding request - simplified and template-driven"""

    brand_id: PositiveInt = Field(..., description="Brand ID for template resolution")
    auto_create_departments: bool = Field(
        True, description="Auto-create departments from templates where auto_create_on_property_launch=true"
    )
    auto_create_profit_centers: bool = Field(
        True, description="Auto-create profit centers from templates where auto_create_on_property_launch=true"
    )
    auto_activate_skus: bool = Field(
        True, description="Automatically activate SKUs based on template configurations"
    )
    enable_tax_calculation: bool = Field(
        True, description="Enable tax calculation with CGST/SGST"
    )


@swag_schema
class TransactionCreationResultSchema(BasePydanticAPISchema):
    """Schema for transaction creation results"""
    
    transaction_code: str = Field(..., description="Generated transaction code")
    transaction_type: str = Field(..., description="Type of transaction")
    entity_type: str = Field(..., description="Entity type")
    status: str = Field(..., description="Creation status")
    gl_code: Optional[str] = Field(None, description="GL code")
    tax_rate: Optional[float] = Field(None, description="Tax rate if applicable")


@swag_schema
class ServiceIntegrationResultSchema(BasePydanticAPISchema):
    """Schema for service integration results"""
    
    service_name: str = Field(..., description="Service name")
    integration_status: str = Field(..., description="Integration status")
    activation_record_id: Optional[str] = Field(None, description="Activation record ID")
    error_message: Optional[str] = Field(None, description="Error message if failed")


@swag_schema
class SkuActivationResultSchema(BasePydanticAPISchema):
    """Schema for SKU activation results"""
    
    sku_code: str = Field(..., description="SKU code")
    sku_category: str = Field(..., description="SKU category")
    activation_status: str = Field(..., description="Activation status")
    department_id: Optional[PositiveInt] = Field(None, description="Assigned department ID")
    error_message: Optional[str] = Field(None, description="Error message if failed")


@swag_schema
class DepartmentCreationResultSchema(BasePydanticAPISchema):
    """Schema for department creation results"""
    
    department_id: PositiveInt = Field(..., description="Created department ID")
    department_code: str = Field(..., description="Department code")
    department_name: str = Field(..., description="Department name")
    template_code: Optional[str] = Field(None, description="Source template code")
    creation_status: str = Field(..., description="Creation status")


@swag_schema
class PropertyOnboardingResponseSchema(BasePydanticAPISchema):
    """Schema for property onboarding response"""
    
    property_id: str = Field(..., description="Property ID")
    onboarding_status: str = Field(..., description="Overall onboarding status")
    started_at: datetime = Field(..., description="Onboarding start time")
    completed_at: Optional[datetime] = Field(None, description="Onboarding completion time")
    
    # Department creation results
    departments_created: List[DepartmentCreationResultSchema] = Field(
        default_factory=list, 
        description="Created departments"
    )
    
    # Transaction generation results
    transactions_created: List[TransactionCreationResultSchema] = Field(
        default_factory=list, 
        description="Generated transactions"
    )
    
    # Tax information
    tax_info: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Tax calculation information"
    )
    
    # SKU activation results
    skus_activated: List[SkuActivationResultSchema] = Field(
        default_factory=list, 
        description="Activated SKUs"
    )
    
    # Service integration results
    service_integrations: List[ServiceIntegrationResultSchema] = Field(
        default_factory=list, 
        description="Service integration results"
    )
    
    # Summary statistics
    summary: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Onboarding summary statistics"
    )
    
    # Error information
    errors: List[str] = Field(
        default_factory=list, 
        description="Any errors encountered during onboarding"
    )
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Additional onboarding metadata"
    )


@swag_schema
class PropertyOnboardingStatusSchema(BasePydanticAPISchema):
    """Schema for property onboarding status check"""
    
    property_id: str = Field(..., description="Property ID")
    is_onboarded: bool = Field(..., description="Whether property is fully onboarded")
    onboarding_progress: float = Field(..., description="Onboarding progress percentage (0-100)")
    last_updated: datetime = Field(..., description="Last update timestamp")
    
    # Status breakdown
    departments_status: str = Field(..., description="Department creation status")
    transactions_status: str = Field(..., description="Transaction generation status")
    sku_activation_status: str = Field(..., description="SKU activation status")
    service_integration_status: str = Field(..., description="Service integration status")
    
    # Counts
    departments_count: int = Field(0, description="Number of departments created")
    transactions_count: int = Field(0, description="Number of transactions generated")
    skus_count: int = Field(0, description="Number of SKUs activated")
    services_count: int = Field(0, description="Number of services integrated")
