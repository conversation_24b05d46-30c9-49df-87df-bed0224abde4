from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes
from cataloging_service.domain.entities.seller.seller import SellerEntity
import logging

logger = logging.getLogger(__name__)


class SellerService:
    def __init__(self, seller_sku_repository, seller_repository, menu_category_repository, restaurant_table_repository):
        self.seller_sku_repository = seller_sku_repository
        self.seller_repository = seller_repository
        self.menu_category_repository = menu_category_repository
        self.restaurant_table_repository = restaurant_table_repository

    def seller_details(self, seller_id, sku_id):
        seller_sku = self.seller_sku_repository.get_seller_sku(seller_id, sku_id)
        return seller_sku

    def get_sellers(self, property_id=None, seller_category_id=None):
        sellers = self.seller_repository.get_sellers(property_id=property_id, seller_category_id=seller_category_id)
        return sellers

    def get_seller_skus(self, seller_id):
        seller_skus = self.seller_sku_repository.get_seller_skus(seller_id=seller_id)
        return seller_skus

    def check_and_raise_error_if_seller_does_not_exist(self, seller_id):
        if not self.seller_repository.does_seller_exist(seller_id=seller_id):
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                    "name": ["Seller ID is not valid"]})

    def get_seller(self, seller_id):
        return self.seller_repository.get_seller(seller_id=seller_id)

    def get_menu_categories(self, seller_category_id=None):
        menu_categories = self.menu_category_repository.get_menu_categories(seller_category_id=seller_category_id)
        return menu_categories

    def get_restaurant_tables(self, seller_id):
        restaurant_tables = self.restaurant_table_repository.get_restaurant_tables(seller_id=seller_id)
        return restaurant_tables

    def create_seller_for_department(self, seller: SellerEntity) -> SellerEntity:
        """Create a seller for property onboarding with department assignment"""
        try:
            # Validate that seller_id is unique
            if self.seller_repository.does_seller_exist(seller.seller_id):
                raise CatalogingServiceException(
                    error_codes.INVALID_REQUEST_DATA,
                    context={"seller_id": [f"Seller with ID '{seller.seller_id}' already exists"]}
                )

            # Create the seller using repository
            created_seller = self.seller_repository.create_seller_for_onboarding(seller)
            logger.info(f"Successfully created seller {created_seller.seller_id} for property {created_seller.property_id}")
            return created_seller

        except Exception as e:
            logger.error(f"Failed to create seller {seller.seller_id}: {str(e)}")
            raise

    def create_seller_for_department(self, seller: SellerEntity):
        self.seller_repository.create_seller_for_onboarding(seller)
