import logging

from datetime import datetime

from cataloging_service.domain import service_provider
from cataloging_service.domain.services.transactions.transaction_default_mapping_service import \
    TransactionDefaultMappingService
from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)

from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.schemas.property_onboarding import PropertyOnboardingRequestSchema, \
    PropertyOnboardingResponseSchema
from object_registry import register_instance

logger = logging.getLogger(__name__)





@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        PropertyDepartmentService,
        TransactionDefaultMappingService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service: DepartmentTemplateService,
        profit_center_template_service: ProfitCenterTemplateService,
        property_department_service: PropertyDepartmentService,
        transaction_default_mapping_service: TransactionDefaultMappingService,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.property_department_service = property_department_service
        self.property_service = service_provider.property_service
        self.meta_service = service_provider.meta_service
        self.seller_service = service_provider.seller_service
        self.sku_service = service_provider.sku_service
        self.transaction_default_mapping_service = transaction_default_mapping_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ) -> PropertyOnboardingResponseSchema:
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,
            template_filters=request.custom_config,
        )

        try:
            # Step 1: Validate property
            property_obj = self._validate_property(request.property_id)

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                departments_created = self._create_departments_from_templates(
                    onboarding_entity,
                )
                onboarding_entity.departments_created = departments_created

            # Step 3: Create profit centers from templates
            # Profit centers are Sellers at property level
            if request.auto_create_profit_centers:
                profit_centers_created = self._create_sellers_from_profit_center_templates(
                    onboarding_entity=onboarding_entity,
                    property_obj=property_obj
                )
                onboarding_entity.profit_centers_created = profit_centers_created

            # Mark as completed
            onboarding_entity.mark_completed()

            return PropertyOnboardingResponseSchema(
                brand_id=onboarding_entity.brand_id,
                onboarding_status=onboarding_entity.onboarding_status,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
                property_id=onboarding_entity.property_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
            )

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )
            return PropertyOnboardingResponseSchema(
                brand_id=onboarding_entity.brand_id,
                onboarding_status=onboarding_entity.onboarding_status,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
                property_id=onboarding_entity.property_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
            )

    def _validate_property(self, property_id: str):
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> int:
        created_department_count = 0
        try:
            department_templates = self.department_template_service.get_auto_create_templates(
                onboarding_entity.brand_id
            )
            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_department_count

            for department_template in department_templates:
                department_entity = self._convert_department_template_to_entity(
                    department_template, onboarding_entity.property_id
                )
                self.property_department_service.create_department(
                    department_entity
                )
                created_department_count += 1
            return created_department_count
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            return created_department_count

    def _convert_department_template_to_entity(self, department_template, property_id: str):
        return PropertyDepartmentEntity(
            property_id=property_id,
            code=department_template.code,
            name=department_template.name,
            parent_id=department_template.parent_code,
            description=department_template.description,
            financial_code=department_template.financial_code,
            is_active=department_template.is_active,
            is_custom=False,
            template_code=department_template.code,
        )

    def _create_sellers_from_profit_center_templates(
        self, onboarding_entity: PropertyOnboardingEntity, property_obj
    ) -> int:
        created_sellers = 0
        try:
            profit_center_templates = (
                self.profit_center_template_service.get_auto_create_templates(
                    onboarding_entity.brand_id
                )
            )

            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_sellers

            for profit_center_template in profit_center_templates:
                try:
                    # Convert template to seller entity with proper field population
                    seller = self._convert_profit_center_template_to_seller_entity(
                        profit_center_template, onboarding_entity.property_id, property_obj
                    )
                    self.seller_service.create_seller_for_department(seller)
                    created_sellers += 1
                    logger.info(f"Created profit center '{profit_center_template.name}' from template {profit_center_template.code}")

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {profit_center_template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_sellers

    def _convert_profit_center_template_to_seller_entity(self, profit_center_template, property_id: str, property_obj):
        seller_id = self._generate_seller_id(property_id, profit_center_template.code)
        default_seller_category_id = 1
        default_city_id = 1
        department = None
        departments = self.property_department_service.get_departments_by_property(property_id)
        for dept in departments:
            if dept.template_code == profit_center_template.department_template_code:
                department = dept
                break
        if not department:
            raise ValueError(f"Department with template code '{profit_center_template.department_template_code}' not found")

        return SellerEntity(
            seller_id=seller_id,
            name=profit_center_template.name,
            property_id=property_id,
            department_id=department.id,
            seller_category_id=default_seller_category_id,
            city_id=default_city_id,
            status="ACTIVE",
            timezone="UTC",
            base_currency_code=property_obj.base_currency_code,
            current_business_date=property_obj.current_business_date,
            created_from_template_code=profit_center_template.code,
            is_auto_created=True,
            legal_name=property_obj.legal_name,
        )

    def _generate_seller_id(self, property_id: str, template_code: str) -> str:
        seller_id = f"{property_id}_{template_code}"
        return seller_id
