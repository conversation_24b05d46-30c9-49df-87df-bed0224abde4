import logging
import hashlib

from datetime import datetime
from tempfile import template
from typing import List, Dict, Any, Optional

from cataloging_service.domain import service_provider

from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)

from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.domain.entities.transactions.transaction_default_mapping import TransactionDefaultMappingEntity
from cataloging_service.domain.services.transactions.transaction_master_service import TransactionMasterService
from cataloging_service.schemas.property_onboarding import PropertyOnboardingRequestSchema, \
    PropertyOnboardingResponseSchema
from object_registry import register_instance

logger = logging.getLogger(__name__)




@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        # SkuTemplateService,
        PropertyDepartmentService,
        # PropertyProfitCenterService,
        # PropertySkuService,
        # TransactionDefaultMappingService,
        # PaymentMethodService,
        TransactionMasterService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service: DepartmentTemplateService,
        profit_center_template_service: ProfitCenterTemplateService,
        # sku_template_service: SkuTemplateService,
        property_department_service: PropertyDepartmentService,
        # property_profit_center_service: PropertyProfitCenterService,
        # property_sku_service: PropertySkuService,
        # transaction_default_mapping_service: TransactionDefaultMappingService,
        # payment_method_service: PaymentMethodService,
        transaction_master_service: TransactionMasterService,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        # self.sku_template_service = sku_template_service
        self.property_department_service = property_department_service
        # self.property_profit_center_service = property_profit_center_service
        # self.property_sku_service = property_sku_service
        self.property_service = service_provider.property_service
        self.meta_service = service_provider.meta_service
        self.seller_service = service_provider.seller_service
        self.sku_service = service_provider.sku_service
        # self.transaction_default_mapping_service = transaction_default_mapping_service
        # self.payment_method_service = payment_method_service
        self.transaction_master_service = transaction_master_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ) -> PropertyOnboardingResponseSchema:
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,
            template_filters=request.custom_config,
        )

        try:
            # Step 1: Validate property
            property_obj = self._validate_property(request.property_id)

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                departments_created = self._create_departments_from_templates(
                    onboarding_entity,
                )
                onboarding_entity.departments_created = departments_created

            # Step 3: Create profit centers from templates
            # Profit centers are Sellers at property level
            if request.auto_create_profit_centers:
                profit_centers_created = self._create_sellers_from_profit_center_templates(
                    onboarding_entity=onboarding_entity
                )
                onboarding_entity.profit_centers_created = profit_centers_created

            # Step 4: Create PropertySKUs with department assignment
            if request.auto_activate_skus:
                skus_created = self._create_property_skus_with_departments(
                    onboarding_entity
                )
                onboarding_entity.skus_created = skus_created

            # Step 5: Transaction Code Generation
            # if request.generate_transaction_codes:
            #     transaction_codes = self._generate_property_transaction_codes(onboarding_entity)
            #     print("transaction_codes", transaction_codes)
            #     onboarding_entity.transaction_codes_generated = len(transaction_codes)

            # Step 6: Service Activation
            # if request.auto_create_skus and request.generate_transaction_codes:
            #     activation_records = self._create_sku_activation_records(onboarding_entity)
            #     print("activation_records", activation_records)

            # Mark as completed
            onboarding_entity.mark_completed()

            # Return response
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
            )

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=datetime.utcnow(),
            )

    def _validate_property(self, property_id: str):
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> int:
        created_department_count = 0
        try:
            department_templates = self.department_template_service.get_auto_create_templates(
                onboarding_entity.brand_id
            )
            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_department_count

            for template in department_templates:
                department_entity = self._convert_department_template_to_entity(
                    template, onboarding_entity.property_id
                )
                self.property_department_service.create_department(
                    department_entity
                )
                created_department_count += 1
            return created_department_count
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            return created_department_count

    def _convert_department_template_to_entity(
        self, template, property_id: str
    ) -> PropertyDepartmentEntity:
        return PropertyDepartmentEntity(
            property_id=property_id,
            template_code=template.code,
            code=template.code,
            name=template.name,
            financial_code=template.financial_code,
            description=template.description,
            is_custom=False,
            is_active=True,
        )

    def _create_sellers_from_profit_center_templates(
        self, onboarding_entity: PropertyOnboardingEntity,
    ) -> int:
        created_sellers = 0
        try:
            profit_center_templates = (
                self.profit_center_template_service.get_profit_center_templates_by_brand(
                    onboarding_entity.brand_id
                )
            )
            print("profit_center_templates", profit_center_templates)

            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_sellers

            for profit_center_template in profit_center_templates:
                try:
                    seller = self._convert_profit_center_template_to_seller_entity(
                        profit_center_template, onboarding_entity.property_id
                    )
                    self.seller_service.create_seller_for_department(
                        seller=seller
                    )
                    created_sellers += 1

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {profit_center_template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_sellers

    def _convert_profit_center_template_to_seller_entity(self, profit_center_template, property_id: str):
        """Convert profit center template to seller entity with proper values"""
        # Generate unique seller ID
        seller_id = self._generate_profit_center_id(property_id, profit_center_template.code)

        # Get property details for city_id and timezone
        property_obj = self.property_service.get_property(property_id)

        # Find department for this profit center template
        department_id = None
        if profit_center_template.department_template_code:
            departments = self.property_department_service.get_departments_by_property(property_id)
            for dept in departments:
                if dept.template_code == profit_center_template.department_template_code:
                    department_id = dept.id
                    break

        return SellerEntity(
            seller_id=seller_id,
            name=profit_center_template.name,
            property_id=property_id,
            seller_category_id=1,  # Default seller category - could be configurable
            city_id=property_obj.city_id if property_obj else 1,
            status="ACTIVE",
            timezone=property_obj.timezone if property_obj else "UTC",
            department_id=department_id,
            created_from_template_code=profit_center_template.code,
            system_interface=profit_center_template.system_interface,
            is_auto_created=True
        )

    def _generate_profit_center_id(self, property_id: str, code: str) -> str:
        """Generate unique profit center ID"""
        base_string = f"{property_id}_{code}_{datetime.now().isoformat()}"
        hash_object = hashlib.md5(base_string.encode())
        short_hash = hash_object.hexdigest()[:8].upper()
        profit_center_id = f"PC_{property_id}_{short_hash}"
        return profit_center_id

    def _create_property_skus_with_departments(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> int:
        """Create PropertySKUs with department assignment based on SKU templates"""
        created_skus_count = 0
        try:
            # Get all SKUs that have default_department_template_code set
            skus_with_dept_templates = self.sku_service.get_skus_with_department_templates()

            if not skus_with_dept_templates:
                onboarding_entity.add_warning(
                    "No SKUs found with department template assignments"
                )
                return created_skus_count

            # Get property departments for mapping
            property_departments = self.property_department_service.get_departments_by_property(
                onboarding_entity.property_id
            )
            dept_mapping = {dept.template_code: dept.id for dept in property_departments if dept.template_code}

            for sku in skus_with_dept_templates:
                try:
                    # Find matching department
                    department_id = dept_mapping.get(sku.default_department_template_code)

                    if not department_id:
                        logger.warning(
                            f"No department found for template code {sku.default_department_template_code} "
                            f"for SKU {sku.code}"
                        )
                        continue

                    # Check if PropertySku already exists
                    existing_property_sku = self.property_service.get_property_sku_by_sku_and_property(
                        sku.id, onboarding_entity.property_id
                    )

                    if existing_property_sku:
                        # Update existing PropertySku with department assignment
                        self.property_service.update_property_sku_department(
                            existing_property_sku.id, department_id
                        )
                        logger.info(f"Updated existing PropertySku {existing_property_sku.id} with department {department_id}")
                    else:
                        # Create new PropertySku with department assignment
                        property_sku_data = {
                            "property_id": onboarding_entity.property_id,
                            "sku_id": sku.id,
                            "department_id": department_id,
                            "rack_rate": sku.default_sale_price or 0.00,
                            "status": "ACTIVE",
                            "saleable": True,
                            "description": f"Auto-created from template for {sku.name}",
                            "extra_information": {"created_from_template": True},
                            "sell_separate": True
                        }

                        new_property_sku = self.property_service.create_property_sku_with_department(
                            property_sku_data
                        )
                        logger.info(f"Created new PropertySku {new_property_sku.id} for SKU {sku.code}")

                    created_skus_count += 1

                except Exception as e:
                    error_msg = f"Failed to create/update PropertySku for SKU {sku.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create PropertySKUs with departments: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_skus_count
