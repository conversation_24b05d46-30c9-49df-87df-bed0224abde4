import logging
import hashlib

from datetime import datetime

from cataloging_service.domain import service_provider
from cataloging_service.utils import Utils

from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)

from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.domain.services.transactions.transaction_master_service import TransactionMasterService
from cataloging_service.schemas.property_onboarding import PropertyOnboardingRequestSchema, \
    PropertyOnboardingResponseSchema
from object_registry import register_instance

logger = logging.getLogger(__name__)





@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        PropertyDepartmentService,
        TransactionMasterService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service: DepartmentTemplateService,
        profit_center_template_service: ProfitCenterTemplateService,
        property_department_service: PropertyDepartmentService,
        transaction_master_service: TransactionMasterService,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.property_department_service = property_department_service
        self.property_service = service_provider.property_service
        self.meta_service = service_provider.meta_service
        self.seller_service = service_provider.seller_service
        self.sku_service = service_provider.sku_service
        self.transaction_master_service = transaction_master_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ):
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,
            template_filters=request.custom_config,
        )

        try:
            # Step 1: Validate property
            property_obj = self._validate_property(request.property_id)

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                departments_created = self._create_departments_from_templates(
                    onboarding_entity,
                )
                onboarding_entity.departments_created = departments_created

            # Step 3: Create profit centers from templates
            # Profit centers are Sellers at property level
            if request.auto_create_profit_centers:
                profit_centers_created = self._create_sellers_from_profit_center_templates(
                    onboarding_entity=onboarding_entity,
                    property_obj=property_obj
                )
                onboarding_entity.profit_centers_created = profit_centers_created



            #Step 4: Transaction Code Generation
            # if request.generate_transaction_codes:
            #     transaction_codes = self._generate_property_transaction_codes(onboarding_entity)
            #     print("transaction_codes", transaction_codes)
            #     onboarding_entity.transaction_codes_generated = len(transaction_codes)


            # Mark as completed
            onboarding_entity.mark_completed()

            # Return response
            # return PropertyOnboardingResponseSchema(
            #     property_id=onboarding_entity.property_id,
            #     brand_id=onboarding_entity.brand_id,
            #     departments_created=onboarding_entity.departments_created,
            #     profit_centers_created=onboarding_entity.profit_centers_created,
            #     skus_created=onboarding_entity.skus_created,
            #     transaction_codes_generated=onboarding_entity.transaction_codes_generated,
            #     onboarding_status=onboarding_entity.onboarding_status,
            #     created_entities=onboarding_entity.created_entities,
            #     errors=onboarding_entity.errors,
            #     warnings=onboarding_entity.warnings,
            #     onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
            # )
            return {
                "property_id": onboarding_entity.property_id,
                "brand_id": onboarding_entity.brand_id,
                "departments_created": onboarding_entity.departments_created,
                "profit_centers_created": onboarding_entity.profit_centers_created,
                "skus_created": onboarding_entity.skus_created,
                "transaction_codes_generated": onboarding_entity.transaction_codes_generated,
                "onboarding_status": onboarding_entity.onboarding_status,
                "created_entities": onboarding_entity.created_entities,
                "errors": onboarding_entity.errors,
                "warnings": onboarding_entity.warnings,
                "onboarded_at": onboarding_entity.onboarded_at or datetime.now(),
            }

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )
            return {
                "property_id": onboarding_entity.property_id,
                "brand_id": onboarding_entity.brand_id,
                "departments_created": onboarding_entity.departments_created,
                "profit_centers_created": onboarding_entity.profit_centers_created,
                "skus_created": onboarding_entity.skus_created,
                "transaction_codes_generated": onboarding_entity.transaction_codes_generated,
                "onboarding_status": onboarding_entity.onboarding_status,
                "created_entities": onboarding_entity.created_entities,
                "errors": onboarding_entity.errors,
                "warnings": onboarding_entity.warnings,
                "onboarded_at": onboarding_entity.onboarded_at or datetime.now(),
            }

    def _validate_property(self, property_id: str):
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> int:
        created_department_count = 0
        try:
            department_templates = self.department_template_service.get_auto_create_templates(
                onboarding_entity.brand_id
            )
            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_department_count

            for department_template in department_templates:
                department_entity = self._convert_department_template_to_entity(
                    department_template, onboarding_entity.property_id
                )
                self.property_department_service.create_department(
                    department_entity
                )
                created_department_count += 1
            return created_department_count
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            return created_department_count

    def _convert_department_template_to_entity(self, department_template, property_id: str):
        return PropertyDepartmentEntity(
            property_id=property_id,
            code=department_template.code,
            name=department_template.name,
            parent_id=department_template.parent_code,
            description=department_template.description,
            financial_code=department_template.financial_code,
            is_active=department_template.is_active,
            is_custom=False,
            template_code=department_template.code,
        )

    def _create_sellers_from_profit_center_templates(
        self, onboarding_entity: PropertyOnboardingEntity, property_obj
    ) -> int:
        created_sellers = 0
        try:
            profit_center_templates = (
                self.profit_center_template_service.get_auto_create_templates(
                    onboarding_entity.brand_id
                )
            )



            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_sellers

            for profit_center_template in profit_center_templates:
                try:
                    # Convert template to seller entity with proper field population
                    seller = self._convert_profit_center_template_to_seller_entity(
                        profit_center_template, onboarding_entity.property_id, property_obj
                    )
                    self.seller_service.create_seller_for_department(seller)
                    created_sellers += 1
                    logger.info(f"Created profit center '{profit_center_template.name}' from template {profit_center_template.code}")

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {profit_center_template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_sellers

    def _convert_profit_center_template_to_seller_entity(self, profit_center_template, property_id: str, property_obj):
        seller_id = self._generate_seller_id(property_id, profit_center_template.code)
        city_id = getattr(property_obj.location, 'city_id', 1) if hasattr(property_obj, 'location') and property_obj.location else 1
        timezone = getattr(property_obj, 'timezone', 'Asia/Kolkata') or 'Asia/Kolkata'
        base_currency_code = getattr(property_obj, 'base_currency_code', 'INR') or 'INR'
        default_seller_category_id = 1
        department_id = None


        return SellerEntity(
            seller_id=seller_id,
            name=profit_center_template.name,
            property_id=property_id,
            seller_category_id=default_seller_category_id,
            city_id=city_id,
            legal_city_id=city_id,  # Use same city for legal address
            status="active",  # Standard status for auto-created sellers
            timezone=timezone,
            base_currency_code=base_currency_code,
            current_business_date=property_obj.current_business_date if hasattr(property_obj, 'current_business_date') else None,
            department_id=department_id,
            created_from_template_code=profit_center_template.code,
            system_interface="PROPERTY_ONBOARDING",
            is_auto_created=True,
            gstin=getattr(property_obj.property_detail, 'gstin', None) if hasattr(property_obj, 'property_detail') and property_obj.property_detail else None,
            legal_name=profit_center_template.name,
            legal_address=getattr(property_obj.location, 'postal_address', None) if hasattr(property_obj, 'location') and property_obj.location else None,
            pincode=getattr(property_obj.location, 'pincode', None) if hasattr(property_obj, 'location') and property_obj.location else None,
            legal_pincode=getattr(property_obj.location, 'pincode', None) if hasattr(property_obj, 'location') and property_obj.location else None,
            phone_number=None,  # Not available from template
            legal_signature=getattr(property_obj.property_detail, 'legal_signature', None) if hasattr(property_obj, 'property_detail') and property_obj.property_detail else None,
            seller_config={}  # Empty config for auto-created sellers
        )

    def _generate_seller_id(self, property_id: str, template_code: str) -> str:
        """Generate unique seller ID for profit center"""
        base_string = f"{property_id}_{template_code}_{Utils.get_epoch_time()}"
        hash_code = Utils.generate_four_digit_hash(base_string)
        seller_id = f"{property_id}_{template_code}_{hash_code}"
        return seller_id

    def _generate_profit_center_id(self, property_id: str, code: str) -> str:
        base_string = f"{property_id}_{code}_{datetime.now().isoformat()}"
        hash_object = hashlib.md5(base_string.encode())
        short_hash = hash_object.hexdigest()[:8].upper()
        profit_center_id = f"PC_{property_id}_{short_hash}"
        return profit_center_id







    # def _generate_property_transaction_codes(
    #     self, onboarding_entity: PropertyOnboardingEntity
    # ) -> List[str]:
    #     transaction_codes = []
    #     try:
    #         for sku_code in onboarding_entity.created_entities.get("skus", []):
    #             transaction_default_mapping = self.transaction_default_mapping_service.get_mappings_by_brand_id(brand_id=onboarding_entity.brand_id)
    #             tx_code = self._create_property_transaction_main(
    #                 onboarding_entity=onboarding_entity,
    #                 sku_code=sku_code,
    #                 template=transaction_default_mapping,
    #             )
    #             transaction_codes.append(tx_code)
    #
    #         if not transaction_codes:
    #             onboarding_entity.add_warning(
    #                 "No transaction codes generated - no SKUs were created"
    #             )
    #
    #     except Exception as e:
    #         error_msg = f"Failed to generate property transaction codes: {str(e)}"
    #         onboarding_entity.add_error(error_msg)
    #         logger.error(error_msg)
    #     return transaction_codes
    #
    #
    # def _create_property_transaction_main(
    #     self, onboarding_entity: PropertyOnboardingEntity, sku_code: str, template: TransactionDefaultMappingEntity
    # ) -> str:
    #     try:
    #         transaction_code = f"TXN_{onboarding_entity.property_id}_{sku_code}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    #         entity = TransactionMasterEntity(
    #             transaction_code=transaction_code,
    #             name=f"Main Transaction - {sku_code}",
    #             property_id=onboarding_entity.property_id,
    #             entity_type=template.entity_type,
    #             transaction_type=template.transaction_type,
    #             transaction_id=sku_code,
    #             operational_unit_id=onboarding_entity.property_id,
    #             operational_unit_type="PROPERTY",
    #             source="PROPERTY_ONBOARDING",
    #             gl_code=template.default_gl_code,
    #             erp_id=template.default_erp_id,
    #             is_merge=template.default_is_merge,
    #             particulars=template.default_particulars,
    #             status="ACTIVE",
    #             transaction_details=template.transaction_details,
    #         )
    #         self.transaction_master_service.create_transaction(entity=entity)
    #         return transaction_code
    #
    #     except Exception as e:
    #         logger.error(f"Failed to create main transaction for {sku_code}: {str(e)}")
    #         raise

