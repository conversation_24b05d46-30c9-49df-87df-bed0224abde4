from typing import List, Optional

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import ValidationException
from object_registry import register_instance
from cataloging_service.domain.entities.templates.profit_center_template import ProfitCenterTemplateEntity
from cataloging_service.infrastructure.repositories.profit_center_template_repository import ProfitCenterTemplateRepository
from sqlalchemy.exc import IntegrityError


@register_instance(dependencies=[ProfitCenterTemplateRepository])
class ProfitCenterTemplateService:
    """Service for profit center template business logic"""

    def __init__(self, repository: ProfitCenterTemplateRepository):
        self.repository: ProfitCenterTemplateRepository = repository

    def create_profit_center_template(self, entity: ProfitCenterTemplateEntity) -> ProfitCenterTemplateEntity:
        """Create a new profit center template with business validation"""
        # Business rule: Check for duplicate code within brand
        if self.repository.exists_by_brand_and_code(entity.brand_id, entity.code):
            raise ValidationException(error_code=error_codes.INVALID_REQUEST_DATA,
                                      error_message=f"Profit center template with code '{entity.code}' already exists "
                                                    f"for brand {entity.brand_id}"
            )
        try:
            return self.repository.create(entity)
        except IntegrityError as e:
            if "brand_id" in str(e.orig):
                raise ValidationException(error_codes.INVALID_REQUEST_DATA,
                                          f"Brand with id '{entity.brand_id}' does not exist")
            raise

    def update_profit_center_template(self, entity: ProfitCenterTemplateEntity) -> ProfitCenterTemplateEntity:
        """Update profit center template with business validation"""
        # Business rule: Check for duplicate code within brand (excluding current)
        if self.repository.exists_by_brand_and_code(entity.brand_id, entity.code, exclude_id=entity.id):
            raise ValidationException(error_code=error_codes.INVALID_REQUEST_DATA,
                                      error_message=f"Profit center template with code '{entity.code}' already exists "
                                                    f"for brand {entity.brand_id}"
            )
        try:
            return self.repository.update(entity)
        except IntegrityError as e:
            if "brand_id" in str(e.orig):
                raise ValidationException(error_codes.INVALID_REQUEST_DATA,
                                          f"Brand with id '{entity.brand_id}' does not exist")
            raise

    def get_profit_center_template_by_id(self, template_id: int) -> Optional[ProfitCenterTemplateEntity]:
        """Get profit center template by ID"""
        return self.repository.get_by_id(template_id)

    def get_profit_center_templates_by_brand(self, brand_id: int, active_only: bool = True) -> List[ProfitCenterTemplateEntity]:
        """Get profit center templates for a brand"""
        return self.repository.get_by_brand(brand_id, active_only)

    def get_by_department_template(self, brand_id: int, department_template_code: str) -> List[ProfitCenterTemplateEntity]:
        """Get profit center templates for a specific department template"""
        return self.repository.get_by_department_template(brand_id, department_template_code)

    def get_auto_create_templates(self, brand_id: int) -> List[ProfitCenterTemplateEntity]:
        """Get profit center templates that should be auto-created on property launch"""
        return self.repository.get_auto_create_templates(brand_id)

    def delete_profit_center_template(self, template_id: int) -> bool:
        """Delete profit center template"""
        return self.repository.delete(template_id)
