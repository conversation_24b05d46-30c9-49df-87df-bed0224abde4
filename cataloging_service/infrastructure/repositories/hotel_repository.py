import datetime
import logging

from sqlalchemy import func, and_, or_
from sqlalchemy.orm import subqueryload, contains_eager, joinedload, load_only, defaultload, selectinload
from treebo_commons.utils import dateutils

from cataloging_service.constants.model_choices import PropertyChoices
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.infrastructure.repositories.pagination import Pagination
from cataloging_service.models import Property, GuestType, Ownership, TransportStation, Room, RoomTypeConfiguration, \
    RoomType, PropertyImage, Restaurant, Bar, BanquetHall, PropertyAmenity, RoomAmenity, RatePlanConfiguration, \
    properties_sku_categories, Landmark, RatePlan, OtaProperty, OTA, FacilityCategoryMapping, AmenitySummary, \
    OtaPropertyMapping, OtaRoomMapping, OtaRatePlanMappings, PropertyLandmark, PropertyDetail, PropertySku, \
    SkuBundle, SkuActivation, Sku, Param, TransportStationProperty, guest_type_property, Location, City, SkuCategory, \
    MicroMarket, BankDetail, Locality, Provider, PropertyPolicy, GuestFacingProcess, Description, \
    NeighbouringPlace, Brand, Owner, State, PropertyVideo
from cataloging_service.thread_locals import app_context
from cataloging_service.constants.constants import NON_INCLUSION_SKU_CATEGORIES

logger = logging.getLogger(__name__)


class PropertyRepository(BaseRepository):

    def get_property(self, property_id):
        return self.pre_filtered_query(Property).filter_by(id=property_id).first()

    def get_property_by_hx_property_id(self, hx_property_id):
        return self.pre_filtered_query(Property).filter_by(hx_id=hx_property_id).first()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            Property: lambda x: x.id,
            Location: lambda x: x.property_id,
            MicroMarket: lambda x: [loc.property_id for loc in x.locations],
            City: lambda x: [loc.property_id for loc in x.locations],
            Locality: lambda x: [loc.property_id for loc in x.locations],
            PropertyDetail: lambda x: x.property_id,
            BankDetail: lambda x: x.property_detail.property_id,
            Provider: lambda x: [pd.property_id for pd in x.property_detail],
            Param: lambda x: None,  # Not sure how to get property_id from Param
            PropertyPolicy: lambda x: [pd.property_id for pd in x.property_details],
            AmenitySummary: lambda x: x.property_id,
            GuestFacingProcess: lambda x: x.property_id,
            Description: lambda x: x.property_id,
            NeighbouringPlace: lambda x: x.property_id,
            SkuCategory: lambda x: [p.id for p in x.properties],
            Brand: lambda x: None,  # Not sure how to get property_id from Brand
            Ownership: lambda x: x.property_id,
            Owner: lambda x: [ownership.property_id for ownership in x.ownerships]
        })
    @cache.multi_key_memoize(attribute_for_cache_key='property_ids', cache_key_id_getter=lambda item: item.id,
                             timeout=7200, unless=lambda: app_context.include_test)
    def get_properties_by_ids(self, property_ids):
        """
        Fetches property and related entities for given list of property_ids.
        Uses `joinedload` for one-to-one relationship. Default join used in `LEFT OUTER JOIN`. For all the one-to-one
        relationship with not-null constraint, we use `INNER JOIN`

        Uses `selectin` load for `one-to-many` relationships (to avoid
        duplication of other rows for every extra rows from many side of relationship)

        :param property_ids:
        :return:
        """
        query = self.pre_filtered_query(Property).filter(Property.id.in_(property_ids))

        query = query.options(
            joinedload(Property.location),
            defaultload(Property.location).options(
                # Nested relationship loading.
                joinedload(Location.micro_market),
                joinedload(Location.city),
                defaultload(Location.city).options(
                    joinedload(City.state),
                    joinedload(City.aliases),
                    defaultload(City.state).options(
                        joinedload(State.country)
                    )
                ),
                joinedload(Location.legal_city),
                defaultload(Location.legal_city).options(
                    joinedload(City.state),
                    joinedload(City.aliases),
                    defaultload(City.state).options(
                        joinedload(State.country)
                    )
                ),
                joinedload(Location.locality)
            )
        )

        query = query.options(
            joinedload(Property.property_detail),
            defaultload(Property.property_detail).options(
                joinedload(PropertyDetail.bank_detail),
                joinedload(PropertyDetail.provider),
                joinedload(PropertyDetail.sold_as),
                # selectinload will hit another SELECT query with property_detail_id IN (...) kind of query
                # The strategy will only query for at most 500 parent primary key values at a time,
                # as the primary keys are rendered into a large IN expression in the SQL statement. Some databases like
                # Oracle have a hard limit on how large an IN expression can be, and overall the size of the SQL string
                # shouldn’t be arbitrarily large.
                # So for large result sets, “selectin” loading will emit a SELECT per 500 parent rows returned. These
                # SELECT statements emit with minimal Python overhead due to the “baked” queries and also minimal SQL
                # overhead as they query against primary key directly.
                # https://docs.sqlalchemy.org/en/13/orm/loading_relationships.html#selectin-eager-loading
                selectinload(PropertyDetail.policies)
            )
        )

        query = query.options(joinedload(Property.amenity_summary))

        query = query.options(joinedload(Property.guest_facing_process))
        query = query.options(joinedload(Property.description))
        query = query.options(joinedload(Property.neighbouring_place))

        query = query.options(selectinload(Property.sku_categories))
        query = query.options(selectinload(Property.brands))
        query = query.options(selectinload('ownerships').joinedload(Ownership.owner))
        return query.all()

    def get_properties_by_ids_with_selective_joinedload(self, property_ids, fields):
        query = self.pre_filtered_query(Property).filter(Property.id.in_(property_ids))
        if not fields or 'location' in fields:
            query = query.options(
                joinedload(Property.location),
                defaultload(Property.location).options(
                    # Nested relationship loading.
                    joinedload(Location.micro_market),
                    joinedload(Location.city),
                    defaultload(Location.city).options(
                        joinedload(City.state),
                        joinedload(City.aliases)
                    ),
                    joinedload(Location.legal_city),
                    defaultload(Location.legal_city).options(
                        joinedload(City.state),
                        joinedload(City.aliases)
                    ),
                    joinedload(Location.locality)
                )
            )

        if not fields or 'property_details' in fields:
            query = query.options(
                joinedload(Property.property_detail),
                defaultload(Property.property_detail).options(
                    joinedload(PropertyDetail.bank_detail),
                    joinedload(PropertyDetail.provider),
                    joinedload(PropertyDetail.sold_as),
                    # selectinload will hit another SELECT query with property_detail_id IN (...) kind of query
                    selectinload(PropertyDetail.policies)
                )
            )

        if not fields or 'amenity_summary' in fields:
            query = query.options(joinedload(Property.amenity_summary))
        if not fields or 'guest_facing_details' in fields:
            query = query.options(joinedload(Property.guest_facing_process))
        if not fields or 'description' in fields:
            query = query.options(joinedload(Property.description))
        if not fields or 'neighbouring_places' in fields:
            query = query.options(joinedload(Property.neighbouring_place))
        if not fields or 'sku_categories' in fields:
            query = query.options(selectinload(Property.sku_categories))
        if not fields or 'brands' in fields:
            query = query.options(selectinload(Property.brands))
        if not fields or 'owners' in fields:
            query = query.options(selectinload('ownerships').joinedload(Ownership.owner))

        return query.all()

    def get_all_properties(self, ids=None, only_id_name=False, include_test_entries=True):
        query = self.pre_filtered_query(Property, include_test=include_test_entries)

        if ids:
            query = query.filter(Property.id.in_(ids))

        if only_id_name:
            query = query.with_entities(Property.id, Property.name)

        return query.all()

    def rget_all_properties_by_status_and_ids_and_sold_as(self, statuses, page, items_per_page, ids=None, sold_as=None):
        query = self.pre_filtered_query(Property)

        # query = query.options(
        #     joinedload(Property.property_detail),
        #     defaultload(Property.property_detail).options(
        #         selectinload(PropertyDetail.policies).load_only('id')
        #     )
        # )

        query = query.options(
            joinedload(Property.location),
            defaultload(Property.location).options(
                joinedload(Location.micro_market),
                joinedload(Location.city),
                defaultload(Location.city).options(
                    joinedload(City.state),
                    joinedload(City.aliases)
                ),
                joinedload(Location.legal_city),
                defaultload(Location.legal_city).options(
                    joinedload(City.state),
                    joinedload(City.aliases)
                ),
                joinedload(Location.locality)
            )
        )

        if sold_as:
            query = query.options(defaultload(Property.property_detail).options(joinedload(PropertyDetail.sold_as)))
            query = query.options(contains_eager(Property.property_detail).contains_eager(PropertyDetail.sold_as)).join(
                PropertyDetail).join(Param).filter(Param.value == str(sold_as).upper())

        # If ids have been asked for
        if ids:
            query = query.filter(Property.id.in_(ids))

        # If status has not been given
        if statuses:
            query = query.filter(Property.status.in_(statuses))

        # If pagination requested
        if page and page > 0:
            return Pagination.paginate(query, page=page, per_page=items_per_page, error_out=False).items

        result = query.all()

        return result

    def rget_all_live_properties(self, ids=None):
        # 1 refers to treebo type properties
        query = self.pre_filtered_query(Property).join(PropertyDetail).filter(PropertyDetail.ext_id == 1)

        if ids:
            query = query.filter(Property.id.in_(ids))

        return query.filter(Property.status.in_([PropertyChoices.STATUS_LIVE, PropertyChoices.STATUS_SIGNED])).all()

    def get_guest_types(self, guest_type_ids):
        return self.session().query(GuestType).filter(GuestType.id.in_(guest_type_ids))

    def get_property_primary_owners(self, property_id):
        ownerships = self.session().query(Ownership).filter_by(primary=True, property_id=property_id)
        return set(ownership.owner for ownership in ownerships)

    def get_transport_station(self, station_type, name):
        return self.session().query(TransportStation).filter(func.lower(TransportStation.name) == func.lower(name),
                                                             TransportStation.type == station_type).first()

    def get_property_room_by_number(self, property_id, room_number):
        return Room.query.filter_by(property_id=property_id, room_number=room_number).first()

    def get_property_room(self, property_id, id):
        return Room.query.filter_by(property_id=property_id, id=id).first()

    def get_room(self, id):
        return Room.query.filter_by(id=id).first()

    def get_property_rooms(self, property_id, room_type_id=None):
        query = Room.query.filter_by(property_id=property_id).options(joinedload(Room.room_type))

        if room_type_id:
            query = query.filter_by(room_type_id=room_type_id)
        query = query.order_by(Room.room_type_id, Room.room_number)
        return query.all()

    def get_property_room_type_configuration_by_room_type(self, property_id, room_type_id):
        return self.session().query(RoomTypeConfiguration).filter_by(property_id=property_id,
                                                                     room_type_id=room_type_id).first()

    def get_property_room_type_configuration_by_mm_id(self, mm_id):
        return self.session().query(RoomTypeConfiguration).filter_by(mm_id=mm_id).first()

    def get_property_room_type_configuration(self, property_id, id=None, room_type=None):
        if id:
            return self.session().query(RoomTypeConfiguration).filter_by(property_id=property_id, id=id).first()
        if room_type:
            return self.session().query(RoomTypeConfiguration).join(RoomType) \
                .filter(RoomType.type == room_type, RoomTypeConfiguration.property_id == property_id).first()

    def get_room_type_configs(self, property_ids):
        query = self.session().query(RoomTypeConfiguration).filter(RoomTypeConfiguration.property_id.in_(property_ids))
        query = query.options(joinedload(RoomTypeConfiguration.room_type), joinedload(RoomTypeConfiguration.provider),
                              selectinload('property_rooms'))
        query = query.order_by(RoomTypeConfiguration.room_type_id)
        return query.all()

    def get_property_room_type_configurations(self, property_id=None):
        query = self.session().query(RoomTypeConfiguration)
        if property_id:
            query = query.filter_by(property_id=property_id)

        return query.all()

    def get_all_room_types(self, ids=None):
        query = self.session().query(RoomType)
        if ids:
            query = query.filter(RoomType.id.in_(ids))
        return query.all()

    def get_all_room_types_by_codes(self, codes=None):
        query = self.session().query(RoomType)
        if codes:
            query = query.filter(RoomType.code.in_(codes))
        return query.all()

    def get_property_rate_plan_configurations(self, property_id):
        return RatePlanConfiguration.query.filter_by(property_id=property_id).all()

    def get_rate_plan_configuration(self, property_id, rate_plan):
        return self.session().query(RatePlanConfiguration).join(RatePlan).filter(
            RatePlanConfiguration.property_id == property_id, RatePlan.plan == rate_plan).first()

    def get_rate_plan_by_name(self, rate_plan):
        return RatePlan.query.filter(RatePlan.plan == rate_plan).first()

    def get_all_rate_plans(self):
        return RatePlan.query.all()

    def get_property_room_count_map(self, property_id):
        result = self.session().query(Room.room_type_id, func.count(Room.room_type_id)).filter(
            Room.property_id == property_id, Room.is_active == True).group_by(Room.room_type_id).all()
        return {r[0]: r[1] for r in result}

    def get_property_room_count(self, property_ids):
        result = self.session().query(Room.property_id, func.count(Room.property_id)).filter(
            Room.property_id.in_(property_ids), Room.is_active == True).group_by(Room.property_id).all()
        return {r[0]: r[1] for r in result}

    def get_room_type_config_room_count(self, room_type_config_ids):
        result = self.session().query(Room.room_type_config_id, func.count(Room.room_type_config_id)).filter(
            Room.room_type_config_id.in_(room_type_config_ids), Room.is_active == True).group_by(
            Room.room_type_config_id).all()
        return {r[0]: r[1] for r in result}

    def get_room_type(self, id):
        return self.session().query(RoomType).get(id)

    def get_room_type_by_name(self, name):
        return self.session().query(RoomType).filter(RoomType.type == name).first()

    def get_property_images(self, property_id):
        return self.session().query(PropertyImage).filter(PropertyImage.property_id == property_id).order_by(
            PropertyImage.sort_order.asc()).all()

    def get_property_videos(self, property_id):
        return self.session().query(PropertyVideo).filter(PropertyVideo.property_id == property_id).order_by(
            PropertyVideo.sort_order.asc()).all()

    def get_all_rooms(self, ids=None):
        query = self.session().query(Room)
        if ids:
            query = query.filter(Room.id.in_(ids))
        return query.all()

    def get_all_room_configs(self, ids=None):
        query = self.session().query(RoomTypeConfiguration)

        if ids:
            query = query.filter(RoomTypeConfiguration.id.in_(ids))

        return query.all()

    def get_all_room_amenities(self, ids=None):
        query = self.session().query(RoomAmenity)

        if ids:
            query = query.filter(RoomAmenity.id.in_(ids))
        return query.all()

    def get_all_restaurants(self, ids=None):
        query = self.session().query(Restaurant)

        if ids:
            query = query.filter(Restaurant.id.in_(ids))

        return query.all()

    def get_all_bars(self, ids=None):
        query = self.session().query(Bar)
        if ids:
            query = query.filter(Bar.id.in_(ids))

        return query.all()

    def get_all_banquet_halls(self, ids=None):
        query = self.session().query(BanquetHall)
        if ids:
            query = query.filter(BanquetHall.id.in_(ids))

        return query.all()

    def get_all_property_amenities(self, ids=None):
        query = self.session().query(PropertyAmenity)

        if ids:
            query = query.filter(PropertyAmenity.id.in_(ids))

        return query.all()

    def get_property_min_room_size_map(self, property_id):
        query_result = self.session().query(Room.room_type_id, func.min(Room.room_size)).filter(
            Room.property_id == property_id).group_by(Room.room_type_id).all()

        return {room_info[0]: room_info[1] for room_info in query_result}

    def get_ota_properties(self, property_id):
        return self.session().query(OtaProperty).filter(OtaProperty.property_id == property_id).all()

    def get_ota_property(self, property_id, ota_code):
        return self.session().query(OtaProperty).join(OTA).filter(OTA.id == OtaProperty.ota_id,
                                                                  OTA.ota_code == ota_code,
                                                                  OtaProperty.property_id == property_id).first()

    def get_ota_property_mapping(self, ota_property_id):
        return self.session().query(OtaPropertyMapping).filter(
            OtaPropertyMapping.ota_property_id == ota_property_id).first()

    def get_ota_room_mapping(self, ota_property_id, room_type_id):
        return self.session().query(OtaRoomMapping).filter(OtaRoomMapping.ota_property_id == ota_property_id,
                                                           OtaRoomMapping.room_type_id == room_type_id).first()

    def get_ota_rate_plan_mapping(self, ota_property_id, room_type_id, rate_plan_id, ota_rate_plan_code):
        return self.session().query(OtaRatePlanMappings).filter(OtaRatePlanMappings.ota_property_id == ota_property_id,
                                                                OtaRatePlanMappings.room_type_id == room_type_id,
                                                                OtaRatePlanMappings.rate_plan_id == rate_plan_id,
                                                                OtaRatePlanMappings.ota_rate_plan_code ==
                                                                ota_rate_plan_code).first()

    def get_ota(self, ota_code):
        return self.session().query(OTA).filter(OTA.ota_code == ota_code).first()

    def get_ota_by_id(self, id):
        return self.session().query(OTA).get(id)

    def get_ota_property_by_id(self, id):
        return self.session().query(OtaProperty).get(id)

    def get_unpushed_unirate_properties(self):
        return self.session().query(OtaProperty).filter(OtaProperty.rcs_callback_complete == True,
                                                        OtaProperty.rcs_push_complete == True).filter(
            or_(OtaProperty.unirate_push_complete == False, OtaProperty.rate_push_complete == False,
                OtaProperty.promo_push_complete == False, OtaProperty.inventory_push_complete == False)).all()

    def get_incomplete_ota_properties(self, since):
        return self.session().query(OtaProperty).filter(OtaProperty.rcs_push_complete == True,
                                                        OtaProperty.rcs_callback_complete == False,
                                                        OtaProperty.rcs_push_time < since).all()

    def get_incomplete_otas(self, since):
        return self.session().query(OTA).filter(OTA.rcs_push_complete == True,
                                                OTA.rcs_callback_complete == False,
                                                OTA.rcs_push_time < since).all()

    def get_facility_mappings(self):
        return self.session().query(FacilityCategoryMapping).all()

    def get_property_amenity_summary(self, property_id):
        return self.session().query(AmenitySummary).filter(AmenitySummary.property_id == property_id).first()

    def get_all_amenity_summaries(self, ids=()):
        query = self.session().query(AmenitySummary)
        if ids:
            query = query.filter(AmenitySummary.id.in_(ids))

        return query.all()

    def get_all_sku_categories_for_properties(self, ids=None):
        query = self.session().query(properties_sku_categories)

        if ids:
            query = query.filter(properties_sku_categories.c.property_id.in_(ids))

        return query.all()

    @cache.multi_key_memoize(attribute_for_cache_key='property_ids', cache_key_id_getter=lambda item: item.property_id,
                             timeout=7200)
    def get_guest_types_for_properties(self, property_ids):
        # TODO: How is this updated? Accordingly register this method to cache expiry
        query = self.session().query(guest_type_property).filter(
            guest_type_property.c.property_id.in_(property_ids)).order_by(guest_type_property.c.guest_type_id)
        return query.all()

    def get_landmarks(self, landmark_id=None, latitude=None, longitude=None):
        query = self.session().query(Landmark)
        if landmark_id:
            return query.get(landmark_id)

        if latitude:
            query = query.filter(Landmark.latitude == latitude)

        if longitude:
            query = query.filter(Landmark.longitude == longitude)

        return query.all()

    def get_landmark_by_name(self, landmark_name):
        return self.session().query(Landmark).filter(func.lower(Landmark.name) == func.lower(landmark_name)).first()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            Landmark: lambda x: x.id
        })
    @cache.multi_key_memoize(attribute_for_cache_key='landmark_ids', cache_key_id_getter=lambda item: item.id,
                             timeout=7200)
    def get_landmarks_by_ids(self, landmark_ids):
        return self.session().query(Landmark).filter(Landmark.id.in_(landmark_ids)).all()

    def get_latest_property_landmark_by_property(self, property_id):
        return self.session().query(PropertyLandmark).filter(PropertyLandmark.property_id == property_id).order_by(
            '-id').all()

    def get_property_landmark_by_property_and_landmark(self, property_id, landmark_id):
        return self.session().query(PropertyLandmark).filter(
            and_(PropertyLandmark.property_id == property_id, PropertyLandmark.landmark_id == landmark_id)).first()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            PropertyLandmark: lambda x: x.property_id,
            Landmark: lambda x: [pl.property_id for pl in x.property_landmarks]
        })
    @cache.multi_key_memoize(attribute_for_cache_key='property_ids', cache_key_id_getter=lambda item: item.property_id,
                             timeout=7200, unless=lambda: app_context.include_test)
    def get_property_landmarks(self, property_ids):
        query = self.session().query(PropertyLandmark).filter(PropertyLandmark.property_id.in_(property_ids))
        query = query.options(joinedload(PropertyLandmark.landmark))
        return query.all()

    def get_properties_by_provider_and_htl_codes(self, provider_id, phtl_codes=None):
        """
        http://docs.sqlalchemy.org/en/rel_0_9/orm/tutorial.html#querying-with-joins
        :param provider_id:
        :param phtl_codes: hotel codes given by provider
        :return:
        """
        query = self.pre_filtered_query(Property).join(PropertyDetail).filter(PropertyDetail.ext_id == provider_id)

        if phtl_codes and hasattr(phtl_codes, "__iter__"):
            query = query.filter(PropertyDetail.provider_hotel_code.in_(phtl_codes))

        return query.all()

    def rget_all_property_skus_by_property_and_sku(self, property_id, sku_codes=None):
        return self.session().query(PropertySku).join(Sku, PropertySku.sku_id == Sku.id) \
            .filter(PropertySku.property_id == property_id, Sku.code.in_(sku_codes)) \
            .all()

    def rcheck_if_stock_exists_by_property_and_service(self, property_id, sku_id, param_service_id):
        return self.session().query(SkuActivation.id) \
                   .filter_by(property_id=property_id, sku_id=sku_id, service_id=param_service_id) \
                   .scalar() is not None

    def rcount_of_stock_for_property_and_all_services(self, property_id, sku_id):
        return self.session().query(func.count(SkuActivation.id)) \
            .filter_by(property_id=property_id, sku_id=sku_id) \
            .scalar()

    def rget_property_sku_by_ids(self, ids=None):
        query = self.session().query(PropertySku)

        if ids and isinstance(ids, list):
            query = query.filter(PropertySku.id.in_(ids))
        return query.all()

    def rget_all_skus_for_property(self, property_id, sku_codes=None, filter=None):
        query = self.session().query(PropertySku).join(PropertySku.sku).options(subqueryload(PropertySku.sku)) \
            .filter(PropertySku.property_id == property_id).order_by(PropertySku.sku_id.asc())
        if sku_codes and isinstance(sku_codes, list):
            query = query.filter(Sku.code.in_(sku_codes))
        if filter and filter.get('for_inclusions'):
            query = query.filter(Sku.is_property_inclusion.is_(True))

        return query.all()

    # @cache.clear_cache_on_model_change(
    #     model_classes_with_cache_key_getter={
    #         PropertySku: lambda x: x.property_id,
    #         Sku: lambda x: [psku.property_id for psku in x.property_skus],
    #         SkuBundle: lambda x: None  # Unable to understand SkuBundle relationship to get property_ids
    #     })
    # @cache.multi_key_memoize(attribute_for_cache_key='cs_ids', cache_key_id_getter=lambda item: item.property_id,
    #                          timeout=1800, unless=lambda: app_context.include_test)
    def get_properties_skus(self, cs_ids):
        # TODO: Disable this db query cache, because if sku_apis.get_all_sku_by_room_configs uses response from
        #  cached result, its wrong. But when read from db query it's valid.
        # TODO: Debug on local why cached result of this DB query is coming incorrect
        """
        :param cs_ids: Catalog hotel_ids
        :return:
        """
        query = self.session().query(PropertySku)
        query = query.options(
            load_only('id', 'property_id', 'display_name', 'saleable', 'status'),
            joinedload(PropertySku.sku).defer('identifier').defer('created_at').defer('modified_at').defer(
                'sku_details').defer('sku_count').defer('category_id').defer('flat_count_for_creation').defer(
                'bundle_rule_id'),
            defaultload(PropertySku.sku).joinedload(Sku.category).load_only('code'),
            defaultload(PropertySku.sku).joinedload(Sku.bundle).load_only('count'),
            defaultload(PropertySku.sku).defaultload(Sku.bundle).joinedload(SkuBundle.sku).load_only(
                'code', 'name', 'saleable', 'hsn_sac'),
            defaultload(PropertySku.sku).defaultload(Sku.bundle).defaultload(SkuBundle.sku).joinedload(
                Sku.category).load_only('code')
        )

        query = query.join(PropertySku.sku).join(Sku.category).options(
            contains_eager(PropertySku.sku).contains_eager(Sku.category))

        query = query.filter(PropertySku.property_id.in_(cs_ids), PropertySku.status == 'ACTIVE',
                             PropertySku.saleable.is_(True),
                             Sku.is_modular.is_(False),
                             SkuCategory.code == 'stay')
        return query.all()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            TransportStationProperty: lambda x: x.property_id,
            TransportStation: lambda x: [tsp.property_id for tsp in x.transport_station_assocs]
        })
    @cache.multi_key_memoize(attribute_for_cache_key='property_ids', cache_key_id_getter=lambda item: item.property_id,
                             timeout=7200)
    def get_property_transport_stations(self, property_ids):
        query = self.session().query(TransportStationProperty).filter(
            TransportStationProperty.property_id.in_(property_ids))
        query = query.options(joinedload(TransportStationProperty.transport_station))
        return query.all()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            TransportStation: lambda x: x.id,
        })
    @cache.multi_key_memoize(attribute_for_cache_key='transport_station_ids', cache_key_id_getter=lambda item: item.id,
                             timeout=7200)
    def get_transport_stations(self, transport_station_ids):
        return self.session().query(TransportStation).filter(TransportStation.id.in_(transport_station_ids)).all()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            PropertyImage: lambda x: x.property_id,
        })
    def get_properties_images(self, property_ids):
        return self.session().query(PropertyImage).filter(PropertyImage.property_id.in_(property_ids)).all()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            PropertyVideo: lambda x: x.property_id,
        })
    def get_properties_videos(self, property_ids):
        return self.session().query(PropertyVideo).filter(PropertyVideo.property_id.in_(property_ids)).all()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            PropertySku: lambda x: x.property_id,
            Sku: lambda x: [psku.property_id for psku in x.property_skus]
        })
    @cache.multi_key_memoize(attribute_for_cache_key='property_ids', cache_key_id_getter=lambda item: item.property_id,
                             timeout=7200, unless=lambda: app_context.include_test)
    def get_property_skus(self, property_ids):
        return self.session().query(PropertySku).filter(PropertySku.property_id.in_(property_ids)).options(
            joinedload(PropertySku.sku)).all()

    def search_properties(self, id=None, statuses=None, city_id=None, order_by_created_at=True):
        query = self.pre_filtered_query(Property).options(
            joinedload(Property.location).joinedload(Location.city).joinedload(City.state).joinedload(State.country),
            joinedload(Property.location).joinedload(Location.locality),
            joinedload(Property.brands)
        )

        if id:
            query = query.filter(Property.id == id)

        if statuses:
            query = query.filter(Property.status.in_(statuses))

        if order_by_created_at:
            query = query.order_by(Property.created_at.asc())

        if city_id:
            query = query.join(Location, Location.property_id == Property.id).filter(Location.city_id == city_id)

        return query

    def get_properties_for_koopan(self):
        properties = self.pre_filtered_query(Property).options(
            load_only("id", "name", "status"),
            joinedload(Property.location).load_only("id").joinedload(Location.city).load_only("id", "name"),
            joinedload(Property.location).load_only("id").joinedload(Location.locality).load_only("id", "name"),
            joinedload(Property.brands)
        ).filter(
            Property.status == PropertyChoices.STATUS_LIVE,
        ).order_by(Property.created_at.asc())
        return properties

    def get_property_timezone(self, property_id):
        return self.session().query(Property.timezone).filter_by(id=property_id)

    def get_property_base_currency(self, property_id):
        return self.session().query(Property.base_currency_code).filter_by(id=property_id)

    def rollover_current_business_date(self, property_id, business_date=None):
        if not business_date:
            logger.info("Incrementing business date for property_id: %s by 1, setting to %s" % (
                property_id, Property.current_business_date + datetime.timedelta(days=1)))
            self.session().query(Property).filter(Property.id == property_id).update(
                {Property.current_business_date: Property.current_business_date + datetime.timedelta(days=1)})
        else:
            logger.info("Updating business date for property_id: %s to %s" % (property_id, business_date))
            self.session().query(Property).filter(Property.id == property_id).update(
                {Property.current_business_date: business_date}
            )

    def get_property_ids(self, statuses=None, modified_date=None):
        query = self.session().query(Property.id)

        if not statuses:
            statuses = [PropertyChoices.STATUS_LIVE]

        if modified_date:
            query.filter(Property.modified_at >= modified_date,
                         Property.modified_at <= dateutils.datetime_at_max_time_of_day(
                             modified_date))

        property_ids = query.filter(Property.status.in_(statuses))
        return [pid[0] for pid in property_ids]

    def get_property_sku_by_sku_and_property(self, sku_id: int, property_id: str):
        """Get PropertySku by SKU ID and property ID"""
        return self.session().query(PropertySku).filter(
            PropertySku.sku_id == sku_id,
            PropertySku.property_id == property_id
        ).first()

    def update_property_sku_department(self, property_sku_id: int, department_id: int):
        """Update PropertySku with department assignment"""
        property_sku = self.session().query(PropertySku).filter(
            PropertySku.id == property_sku_id
        ).first()

        if property_sku:
            property_sku.department_id = department_id
            self.session().flush()
            return property_sku
        return None

    def create_properties_sku_categories(self, property_id, sku_categories_id):
        for category_id in sku_categories_id:
            insert_sku_categories = properties_sku_categories.insert().values(property_id=property_id,
                                                                              sku_category_id=category_id)
            self.session().execute(insert_sku_categories)

    def get_test_property_ids(self):
        return [property.id for property in self.session().query(Property.id).filter(Property.is_test == True).all()]
