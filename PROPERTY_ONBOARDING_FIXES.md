# Property Onboarding Flow - Bug Fixes

## Issues Fixed

### 1. **Duplicate Department Creation Error**
**Error**: `Department with code 'FINANCE' already exists for property 0001263`

**Root Cause**: The onboarding flow was trying to create departments without checking if they already existed.

**Fix Applied**:
- Added duplicate check before creating departments
- Skip creation if department with same code already exists for the property
- Log informational message when skipping existing departments

**Code Changes**:
```python
# Check if department already exists
existing_departments = self.property_department_service.get_departments_by_property(
    onboarding_entity.property_id, active_only=False
)
existing_codes = {dept.code for dept in existing_departments}

if template.code in existing_codes:
    logger.info(f"Department {template.code} already exists for property {onboarding_entity.property_id}, skipping")
    continue
```

### 2. **Property Object Missing Attributes Error**
**Error**: `'Property' object has no attribute 'city_id'`

**Root Cause**: The property object structure was different than expected - `city_id` might be nested under `location.city.id` or similar.

**Fix Applied**:
- Added safe attribute access with fallbacks
- Handle different property object structures
- Provide sensible defaults when attributes are missing

**Code Changes**:
```python
# Extract property attributes safely
city_id = 1  # Default city_id
timezone = "UTC"  # Default timezone

if property_obj:
    # Handle different property object types/structures
    if hasattr(property_obj, 'city_id'):
        city_id = property_obj.city_id
    elif hasattr(property_obj, 'location') and hasattr(property_obj.location, 'city_id'):
        city_id = property_obj.location.city_id
    elif hasattr(property_obj, 'location') and hasattr(property_obj.location, 'city') and hasattr(property_obj.location.city, 'id'):
        city_id = property_obj.location.city.id
    
    if hasattr(property_obj, 'timezone'):
        timezone = property_obj.timezone or "UTC"
```

### 3. **Duplicate Seller Creation Prevention**
**Issue**: Similar to departments, sellers could be created multiple times.

**Fix Applied**:
- Added duplicate check for sellers based on template codes
- Skip creation if seller from same template already exists
- Use `get_auto_create_templates()` method to only get templates marked for auto-creation

**Code Changes**:
```python
# Check if seller already exists for this template
existing_sellers = self.seller_service.get_sellers(property_id=onboarding_entity.property_id)
existing_template_codes = {
    seller.created_from_template_code for seller in existing_sellers 
    if seller.created_from_template_code
}

if profit_center_template.code in existing_template_codes:
    logger.info(f"Seller from template {profit_center_template.code} already exists for property {onboarding_entity.property_id}, skipping")
    continue
```

### 4. **Enhanced Template Filtering**
**Improvement**: Use proper auto-create template methods instead of filtering manually.

**Changes**:
- Added `get_auto_create_templates()` method to `ProfitCenterTemplateService`
- Added corresponding repository method `get_auto_create_templates()`
- Use filtered templates instead of getting all templates and checking flags

**Code Changes**:
```python
# Before: Get all templates and filter manually
profit_center_templates = self.profit_center_template_service.get_profit_center_templates_by_brand(brand_id)

# After: Get only auto-create templates
profit_center_templates = self.profit_center_template_service.get_auto_create_templates(brand_id)
```

### 5. **Better Error Handling and Logging**
**Improvements**:
- Added individual try-catch blocks for each template processing
- Enhanced logging with informational messages for skipped items
- Better error messages with specific template codes
- Graceful handling of missing attributes

## Repository Methods Added

### ProfitCenterTemplateRepository
```python
def get_auto_create_templates(self, brand_id: int) -> List[ProfitCenterTemplateEntity]:
    """Get profit center templates that should be auto-created on property launch"""
    models = self.rget_by_attr(
        ProfitCenterTemplate,
        brand_id=brand_id,
        is_active=True,
        auto_create_on_property_launch=True,
    )
    return [self.adaptor.to_entity(model) for model in models]
```

### ProfitCenterTemplateService
```python
def get_auto_create_templates(self, brand_id: int) -> List[ProfitCenterTemplateEntity]:
    """Get profit center templates that should be auto-created on property launch"""
    return self.repository.get_auto_create_templates(brand_id)
```

## Testing

Updated test script to use existing property ID (`0001263`) instead of test property to ensure realistic testing conditions.

## Result

The property onboarding flow now:
1. ✅ **Handles existing departments gracefully** - skips duplicates instead of failing
2. ✅ **Works with different property object structures** - safely extracts attributes with fallbacks
3. ✅ **Prevents duplicate seller creation** - checks existing sellers before creating new ones
4. ✅ **Uses proper template filtering** - only processes templates marked for auto-creation
5. ✅ **Provides better error reporting** - specific error messages and warnings
6. ✅ **Maintains idempotency** - can be run multiple times safely

## Usage

The onboarding flow can now be called multiple times for the same property without errors:

```bash
# First run - creates departments and sellers
POST /api/properties/0001263/onboard

# Second run - skips existing items, no errors
POST /api/properties/0001263/onboard
```

Both calls will succeed, with the second call logging informational messages about skipped items instead of throwing errors.
