# Property Onboarding Testing Guide

## Overview
This guide provides comprehensive testing instructions for the property onboarding flow, including unit tests, integration tests, and API testing.

## Test Files Created

### 1. `test_property_onboarding_comprehensive.py`
**Purpose**: Comprehensive integration and API testing
**Features**:
- Full property onboarding flow testing
- Schema validation testing
- Error scenario testing
- Idempotency testing
- API endpoint testing
- Test report generation

### 2. `test_property_onboarding_unit.py`
**Purpose**: Unit testing with pytest framework
**Features**:
- Schema validation unit tests
- Entity business logic tests
- Service method unit tests
- Mock-based testing
- Integration compatibility tests

## Running the Tests

### Prerequisites
```bash
# Install required dependencies
pip install requests pytest

# Ensure cataloging service is running
# Default: http://localhost:5000
```

### Comprehensive Integration Tests

#### Run Full Test Suite
```bash
python test_property_onboarding_comprehensive.py
```

#### Run Quick Test
```bash
python test_property_onboarding_comprehensive.py --quick
```

#### Customize Test Parameters
```python
# Edit the test file to change these values:
property_id = "0001263"  # Your property ID
brand_id = 1            # Your brand ID
base_url = "http://localhost:5000"  # Your API base URL
```

### Unit Tests

#### Run All Unit Tests
```bash
pytest test_property_onboarding_unit.py -v
```

#### Run Specific Test Classes
```bash
# Test only schemas
pytest test_property_onboarding_unit.py::TestPropertyOnboardingSchemas -v

# Test only entities
pytest test_property_onboarding_unit.py::TestPropertyOnboardingEntity -v

# Test only services
pytest test_property_onboarding_unit.py::TestPropertyOnboardingService -v
```

#### Run Specific Test Methods
```bash
# Test specific method
pytest test_property_onboarding_unit.py::TestPropertyOnboardingSchemas::test_request_schema_valid -v
```

#### Generate Coverage Report
```bash
# Install coverage
pip install pytest-cov

# Run with coverage
pytest test_property_onboarding_unit.py --cov=cataloging_service.domain.services.template.property_onboarding_service --cov-report=html
```

## Test Scenarios Covered

### 1. Basic Functionality Tests
- ✅ **Department Creation**: Auto-create departments from templates
- ✅ **Seller Creation**: Auto-create sellers (profit centers) from templates
- ✅ **PropertySku Creation**: Create PropertySKUs with department assignment
- ✅ **City Resolution**: Get city object from property with multiple fallback paths
- ✅ **Template Filtering**: Use only auto-create templates

### 2. Schema Validation Tests
- ✅ **Request Schema**: Valid/invalid request payloads
- ✅ **Response Schema**: Proper response structure
- ✅ **Field Validation**: Required fields, data types, constraints
- ✅ **Default Values**: Proper default value handling
- ✅ **Boundary Values**: Edge cases and limits

### 3. Error Handling Tests
- ✅ **Invalid Property ID**: Non-existent property handling
- ✅ **Invalid Brand ID**: Non-existent brand handling
- ✅ **Missing Fields**: Required field validation
- ✅ **Duplicate Creation**: Handling existing departments/sellers
- ✅ **Property Attribute Errors**: Missing city_id, timezone, etc.

### 4. Business Logic Tests
- ✅ **Idempotency**: Multiple runs don't create duplicates
- ✅ **Selective Creation**: Enable/disable specific features
- ✅ **Custom Configuration**: Template filtering and overrides
- ✅ **Entity State Management**: Proper status transitions
- ✅ **Error Recovery**: Graceful error handling

### 5. API Endpoint Tests
- ✅ **HTTP Methods**: Correct method validation
- ✅ **Content Types**: Proper content type handling
- ✅ **Status Codes**: Appropriate HTTP status responses
- ✅ **Response Format**: JSON structure validation

## Expected Test Results

### Successful Onboarding Response
```json
{
  "property_id": "0001263",
  "brand_id": 1,
  "departments_created": 3,
  "profit_centers_created": 2,
  "onboarding_status": "COMPLETED",
  "errors": [],
  "warnings": [],
  "onboarded_at": "2025-01-26T10:30:00Z"
}
```

### Error Response Example
```json
{
  "property_id": "INVALID_PROP",
  "brand_id": 1,
  "departments_created": 0,
  "profit_centers_created": 0,
  "onboarding_status": "FAILED",
  "errors": [
    "Property with ID 'INVALID_PROP' not found"
  ],
  "warnings": [],
  "onboarded_at": "2025-01-26T10:30:00Z"
}
```

### Idempotency Test Results
```
🔍 Idempotency Analysis:
   ✅ departments_created: 0 (consistent)
   ✅ profit_centers_created: 0 (consistent)
   ✅ onboarding_status: COMPLETED (consistent)
   ✅ Idempotency: Second run correctly skipped existing items
```

## Test Data Requirements

### Database Setup
Ensure your test database has:
- Valid property with ID `0001263` (or update test to use existing property)
- Brand with ID `1` with associated templates
- Department templates with `auto_create_on_property_launch=true`
- Profit center templates with `auto_create_on_property_launch=true`
- SKUs with `default_department_template_code` set

### Template Configuration
```sql
-- Example department templates
INSERT INTO department_templates (brand_id, code, name, auto_create_on_property_launch) 
VALUES 
(1, 'FINANCE', 'Finance Department', true),
(1, 'HOUSEKEEPING', 'Housekeeping Department', true);

-- Example profit center templates
INSERT INTO profit_center_templates (brand_id, code, name, department_template_code, auto_create_on_property_launch)
VALUES
(1, 'FRONTDESK', 'Front Desk', 'FINANCE', true),
(1, 'BAR', 'Bar', 'FINANCE', true);
```

## Troubleshooting

### Common Issues

#### Connection Errors
```
❌ Connection error: Could not connect to http://localhost:5000
```
**Solution**: Ensure cataloging service is running on the correct port

#### Property Not Found
```
❌ Property with ID '0001263' not found
```
**Solution**: Update test with existing property ID or create test property

#### Template Not Found
```
⚠️ No department templates found for auto-creation
```
**Solution**: Ensure templates exist with `auto_create_on_property_launch=true`

#### Schema Validation Errors
```
❌ Invalid brand_id type not properly validated
```
**Solution**: Check Pydantic schema configuration and validation rules

### Debug Mode
Enable debug logging in tests:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Manual API Testing
Use curl for manual testing:
```bash
curl -X POST http://localhost:5000/api/property/onboard \
  -H "Content-Type: application/json" \
  -d '{
    "property_id": "0001263",
    "brand_id": 1,
    "auto_create_departments": true,
    "auto_create_profit_centers": true,
    "custom_config": {}
  }'
```

## Performance Testing

### Load Testing
For load testing, use the comprehensive test with multiple concurrent requests:
```python
import concurrent.futures
import threading

def run_concurrent_tests(num_threads=5):
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(run_quick_test) for _ in range(num_threads)]
        results = [future.result() for future in futures]
    return results
```

### Memory Testing
Monitor memory usage during tests:
```bash
# Install memory profiler
pip install memory-profiler

# Run with memory profiling
python -m memory_profiler test_property_onboarding_comprehensive.py
```

## Continuous Integration

### GitHub Actions Example
```yaml
name: Property Onboarding Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run unit tests
      run: pytest test_property_onboarding_unit.py --cov --cov-report=xml
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Mock External Dependencies**: Use mocks for external services
3. **Data Cleanup**: Clean up test data after tests
4. **Assertion Quality**: Use specific, meaningful assertions
5. **Error Testing**: Test both success and failure scenarios
6. **Documentation**: Keep tests well-documented and maintainable

## Reporting Issues

When reporting test failures, include:
- Test command used
- Full error output
- Environment details (Python version, OS, etc.)
- Database state (if applicable)
- API response logs

This comprehensive testing approach ensures the property onboarding flow is robust, reliable, and maintainable.
